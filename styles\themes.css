/* 主题样式文件 */

/* 暗黑主题 */
.theme-dark {
    --primary-color: #8f7a66;
    --secondary-color: #2c2c2c;
    --background-color: #1a1a1a;
    --text-color: #f9f6f2;
    --border-color: #444;
    --tile-background: #333;
    --tile-2: #3c3c3c;
    --tile-4: #4a4a4a;
    --tile-8: #5a5a5a;
    --tile-16: #6a6a6a;
    --tile-32: #7a7a7a;
    --tile-64: #8a8a8a;
    --tile-128: #9a9a9a;
    --tile-256: #aaa;
    --tile-512: #bbb;
    --tile-1024: #ccc;
    --tile-2048: #ddd;
    --tile-super: #eee;
}

/* 彩虹主题 */
.theme-rainbow {
    --primary-color: #ff6b6b;
    --secondary-color: #ffe66d;
    --background-color: #f8f9fa;
    --text-color: #495057;
    --border-color: #dee2e6;
    --tile-background: #e9ecef;
    --tile-2: #ff9ff3;
    --tile-4: #54a0ff;
    --tile-8: #5f27cd;
    --tile-16: #00d2d3;
    --tile-32: #ff9f43;
    --tile-64: #ee5a24;
    --tile-128: #0abde3;
    --tile-256: #006ba6;
    --tile-512: #8e44ad;
    --tile-1024: #2d3436;
    --tile-2048: #fd79a8;
    --tile-super: #6c5ce7;
}

/* 霓虹主题 */
.theme-neon {
    --primary-color: #00ffff;
    --secondary-color: #1a1a2e;
    --background-color: #0f0f23;
    --text-color: #00ffff;
    --border-color: #16213e;
    --tile-background: #16213e;
    --tile-2: #ff006e;
    --tile-4: #8338ec;
    --tile-8: #3a86ff;
    --tile-16: #06ffa5;
    --tile-32: #ffbe0b;
    --tile-64: #fb5607;
    --tile-128: #ff006e;
    --tile-256: #8338ec;
    --tile-512: #3a86ff;
    --tile-1024: #06ffa5;
    --tile-2048: #ffbe0b;
    --tile-super: #fb5607;
}

.theme-neon .tile {
    box-shadow: 0 0 10px currentColor;
    border: 1px solid currentColor;
}

/* 自然主题 */
.theme-nature {
    --primary-color: #2d5016;
    --secondary-color: #f1f8e9;
    --background-color: #e8f5e8;
    --text-color: #2d5016;
    --border-color: #8bc34a;
    --tile-background: #c8e6c9;
    --tile-2: #dcedc8;
    --tile-4: #c5e1a5;
    --tile-8: #aed581;
    --tile-16: #9ccc65;
    --tile-32: #8bc34a;
    --tile-64: #7cb342;
    --tile-128: #689f38;
    --tile-256: #558b2f;
    --tile-512: #33691e;
    --tile-1024: #1b5e20;
    --tile-2048: #2e7d32;
    --tile-super: #388e3c;
}

/* 海洋主题 */
.theme-ocean {
    --primary-color: #0d47a1;
    --secondary-color: #e3f2fd;
    --background-color: #e1f5fe;
    --text-color: #0d47a1;
    --border-color: #03a9f4;
    --tile-background: #b3e5fc;
    --tile-2: #e1f5fe;
    --tile-4: #b3e5fc;
    --tile-8: #81d4fa;
    --tile-16: #4fc3f7;
    --tile-32: #29b6f6;
    --tile-64: #03a9f4;
    --tile-128: #039be5;
    --tile-256: #0288d1;
    --tile-512: #0277bd;
    --tile-1024: #01579b;
    --tile-2048: #0d47a1;
    --tile-super: #1565c0;
}

/* 主题选择器 */
.theme-selector {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    max-width: 400px;
    width: 90%;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.theme-item {
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: 2px solid transparent;
    font-weight: 500;
}

.theme-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.theme-item.active {
    border-color: var(--primary-color);
}

.theme-item[data-theme="classic"] {
    background: linear-gradient(135deg, #eee4da, #f2b179);
    color: #776e65;
}

.theme-item[data-theme="dark"] {
    background: linear-gradient(135deg, #2c2c2c, #666);
    color: #f9f6f2;
}

.theme-item[data-theme="rainbow"] {
    background: linear-gradient(135deg, #ff9ff3, #54a0ff, #5f27cd);
    color: white;
}

.theme-item[data-theme="neon"] {
    background: linear-gradient(135deg, #0f0f23, #16213e);
    color: #00ffff;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.theme-item[data-theme="nature"] {
    background: linear-gradient(135deg, #dcedc8, #8bc34a);
    color: #2d5016;
}

.theme-item[data-theme="ocean"] {
    background: linear-gradient(135deg, #e1f5fe, #03a9f4);
    color: #0d47a1;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--secondary-color);
    border-left: 1px solid var(--border-color);
    transition: right 0.3s ease-in-out;
    z-index: 999;
    overflow-y: auto;
}

.sidebar.show {
    right: 0;
}

.sidebar-content {
    padding: 20px;
}

.sidebar h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.menu-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.menu-section:last-child {
    border-bottom: none;
}

.menu-section h4 {
    margin-bottom: 15px;
    color: var(--text-color);
    font-size: 1.1rem;
}

/* 统计数据样式 */
.statistics {
    display: grid;
    gap: 10px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-label {
    font-weight: 500;
}

.stat-value {
    color: var(--primary-color);
    font-weight: 700;
}

/* 成就样式 */
.achievements {
    display: grid;
    gap: 10px;
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.achievement-item.unlocked {
    background: #d4edda;
    border-color: #28a745;
}

.achievement-icon {
    width: 30px;
    height: 30px;
    margin-right: 10px;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.achievement-info {
    flex: 1;
}

.achievement-title {
    font-weight: 500;
    margin-bottom: 2px;
}

.achievement-description {
    font-size: 12px;
    opacity: 0.8;
}

/* 设置样式 */
.settings {
    display: grid;
    gap: 15px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    background: var(--background-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.setting-item:hover {
    background: var(--tile-background);
}

.setting-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

/* 数据控制样式 */
.data-controls {
    display: grid;
    gap: 10px;
}

/* 教程样式 */
.tutorial {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.tutorial-content {
    background: white;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.tutorial-steps {
    margin: 20px 0;
    min-height: 200px;
}

.tutorial-step {
    display: none;
}

.tutorial-step.active {
    display: block;
}

.tutorial-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

/* 粒子系统 */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 50;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        right: -100%;
    }
    
    .theme-selector {
        width: 95%;
    }
    
    .theme-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
