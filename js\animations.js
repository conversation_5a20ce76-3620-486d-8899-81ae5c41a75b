// 动画管理文件
class AnimationManager {
    constructor() {
        this.enabled = true;
        this.animationQueue = [];
        this.isAnimating = false;
        this.init();
    }

    init() {
        this.loadSettings();
        this.bindEvents();
    }

    loadSettings() {
        const settings = storage.get('settings', {});
        this.enabled = settings.animation !== false;
    }

    bindEvents() {
        // 监听设置变化
        const animationToggle = document.getElementById('animationToggle');
        if (animationToggle) {
            animationToggle.addEventListener('change', (e) => {
                this.setEnabled(e.target.checked);
            });
        }
    }

    setEnabled(enabled) {
        this.enabled = enabled;
        
        // 保存设置
        const settings = storage.get('settings', {});
        settings.animation = enabled;
        storage.set('settings', settings);
        
        // 如果禁用动画，添加no-animations类
        if (!enabled) {
            document.body.classList.add('no-animations');
        } else {
            document.body.classList.remove('no-animations');
        }
    }

    isEnabled() {
        return this.enabled;
    }

    // 瓦片移动动画
    animateTileMove(tile, fromPos, toPos, duration = 150) {
        if (!this.enabled) return Promise.resolve();

        return new Promise((resolve) => {
            const deltaX = toPos.x - fromPos.x;
            const deltaY = toPos.y - fromPos.y;
            
            tile.style.setProperty('--move-x', deltaX + 'px');
            tile.style.setProperty('--move-y', deltaY + 'px');
            tile.classList.add('animate-move');
            
            setTimeout(() => {
                tile.classList.remove('animate-move');
                tile.style.removeProperty('--move-x');
                tile.style.removeProperty('--move-y');
                resolve();
            }, duration);
        });
    }

    // 瓦片出现动画
    animateTileAppear(tile, delay = 0) {
        if (!this.enabled) return Promise.resolve();

        return new Promise((resolve) => {
            setTimeout(() => {
                tile.classList.add('animate-appear');
                
                setTimeout(() => {
                    tile.classList.remove('animate-appear');
                    resolve();
                }, 200);
            }, delay);
        });
    }

    // 瓦片合并动画
    animateTileMerge(tile, delay = 0) {
        if (!this.enabled) return Promise.resolve();

        return new Promise((resolve) => {
            setTimeout(() => {
                tile.classList.add('animate-merge');
                
                setTimeout(() => {
                    tile.classList.remove('animate-merge');
                    resolve();
                }, 200);
            }, delay);
        });
    }

    // 分数增加动画
    animateScoreIncrease(element, newScore, oldScore) {
        if (!this.enabled) return;

        element.classList.add('animate-score');
        
        // 数字滚动效果
        this.animateNumber(element, oldScore, newScore, 300);
        
        setTimeout(() => {
            element.classList.remove('animate-score');
        }, 300);
    }

    // 数字滚动动画
    animateNumber(element, from, to, duration = 1000) {
        if (!this.enabled) {
            element.textContent = Utils.formatNumber(to);
            return;
        }

        const startTime = performance.now();
        const difference = to - from;
        
        const updateNumber = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easedProgress = Utils.easeInOut(progress);
            const currentValue = Math.round(from + (difference * easedProgress));
            
            element.textContent = Utils.formatNumber(currentValue);
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        };
        
        requestAnimationFrame(updateNumber);
    }

    // 游戏板震动动画
    animateBoardShake() {
        if (!this.enabled) return;

        const gameBoard = document.getElementById('gameBoard');
        if (gameBoard) {
            gameBoard.classList.add('animate-shake');
            
            setTimeout(() => {
                gameBoard.classList.remove('animate-shake');
            }, 500);
        }
    }

    // 游戏板脉冲动画
    animateBoardPulse() {
        if (!this.enabled) return;

        const gameBoard = document.getElementById('gameBoard');
        if (gameBoard) {
            gameBoard.classList.add('animate-pulse');
            
            setTimeout(() => {
                gameBoard.classList.remove('animate-pulse');
            }, 300);
        }
    }

    // 按钮按下动画
    animateButtonPress(button) {
        if (!this.enabled) return;

        button.classList.add('animate-button-press');
        
        setTimeout(() => {
            button.classList.remove('animate-button-press');
        }, 100);
    }

    // 元素发光动画
    animateGlow(element, duration = 2000) {
        if (!this.enabled) return;

        element.classList.add('animate-glow');
        
        setTimeout(() => {
            element.classList.remove('animate-glow');
        }, duration);
    }

    // 淡入动画
    animateFadeIn(element, duration = 300) {
        if (!this.enabled) {
            element.style.opacity = '1';
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            element.style.opacity = '0';
            element.style.transition = `opacity ${duration}ms ease-in-out`;
            
            // 强制重排
            element.offsetHeight;
            
            element.style.opacity = '1';
            
            setTimeout(() => {
                element.style.transition = '';
                resolve();
            }, duration);
        });
    }

    // 淡出动画
    animateFadeOut(element, duration = 300) {
        if (!this.enabled) {
            element.style.opacity = '0';
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            element.style.transition = `opacity ${duration}ms ease-in-out`;
            element.style.opacity = '0';
            
            setTimeout(() => {
                element.style.transition = '';
                resolve();
            }, duration);
        });
    }

    // 滑入动画
    animateSlideIn(element, direction = 'up', duration = 300) {
        if (!this.enabled) return Promise.resolve();

        return new Promise((resolve) => {
            const transforms = {
                up: 'translateY(50px)',
                down: 'translateY(-50px)',
                left: 'translateX(50px)',
                right: 'translateX(-50px)'
            };
            
            element.style.transform = transforms[direction];
            element.style.opacity = '0';
            element.style.transition = `transform ${duration}ms ease-out, opacity ${duration}ms ease-out`;
            
            // 强制重排
            element.offsetHeight;
            
            element.style.transform = 'translate(0, 0)';
            element.style.opacity = '1';
            
            setTimeout(() => {
                element.style.transition = '';
                element.style.transform = '';
                resolve();
            }, duration);
        });
    }

    // 缩放动画
    animateScale(element, fromScale = 0, toScale = 1, duration = 300) {
        if (!this.enabled) {
            element.style.transform = `scale(${toScale})`;
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            element.style.transform = `scale(${fromScale})`;
            element.style.transition = `transform ${duration}ms ease-out`;
            
            // 强制重排
            element.offsetHeight;
            
            element.style.transform = `scale(${toScale})`;
            
            setTimeout(() => {
                element.style.transition = '';
                resolve();
            }, duration);
        });
    }

    // 旋转动画
    animateRotate(element, degrees = 360, duration = 1000) {
        if (!this.enabled) return Promise.resolve();

        return new Promise((resolve) => {
            element.style.transition = `transform ${duration}ms ease-in-out`;
            element.style.transform = `rotate(${degrees}deg)`;
            
            setTimeout(() => {
                element.style.transition = '';
                element.style.transform = '';
                resolve();
            }, duration);
        });
    }

    // 弹跳动画
    animateBounce(element, height = 20, duration = 600) {
        if (!this.enabled) return Promise.resolve();

        return new Promise((resolve) => {
            const keyframes = [
                { transform: 'translateY(0px)', offset: 0 },
                { transform: `translateY(-${height}px)`, offset: 0.5 },
                { transform: 'translateY(0px)', offset: 1 }
            ];
            
            const animation = element.animate(keyframes, {
                duration: duration,
                easing: 'ease-out'
            });
            
            animation.onfinish = () => resolve();
        });
    }

    // 摇摆动画
    animateWobble(element, angle = 15, duration = 600) {
        if (!this.enabled) return Promise.resolve();

        return new Promise((resolve) => {
            const keyframes = [
                { transform: 'rotate(0deg)', offset: 0 },
                { transform: `rotate(${angle}deg)`, offset: 0.25 },
                { transform: `rotate(-${angle}deg)`, offset: 0.75 },
                { transform: 'rotate(0deg)', offset: 1 }
            ];
            
            const animation = element.animate(keyframes, {
                duration: duration,
                easing: 'ease-in-out'
            });
            
            animation.onfinish = () => resolve();
        });
    }

    // 打字机效果
    animateTypewriter(element, text, speed = 50) {
        if (!this.enabled) {
            element.textContent = text;
            return Promise.resolve();
        }

        return new Promise((resolve) => {
            element.textContent = '';
            let i = 0;
            
            const typeChar = () => {
                if (i < text.length) {
                    element.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeChar, speed);
                } else {
                    resolve();
                }
            };
            
            typeChar();
        });
    }

    // 粒子爆炸动画
    animateParticleExplosion(x, y, options = {}) {
        if (!this.enabled || !particleSystem.isEnabled()) return;

        particleSystem.explode(x, y, {
            count: options.count || 20,
            colors: options.colors || ['#f67c5f', '#f59563', '#f2b179'],
            speed: options.speed || 3,
            life: options.life || 1,
            ...options
        });
    }

    // 连续动画队列
    animateSequence(animations) {
        if (!this.enabled) return Promise.resolve();

        return animations.reduce((promise, animation) => {
            return promise.then(() => animation());
        }, Promise.resolve());
    }

    // 并行动画
    animateParallel(animations) {
        if (!this.enabled) return Promise.resolve();

        return Promise.all(animations.map(animation => animation()));
    }

    // 延迟动画
    animateDelay(duration) {
        return new Promise(resolve => setTimeout(resolve, duration));
    }

    // 创建CSS关键帧动画
    createKeyframeAnimation(name, keyframes, duration = 1000, easing = 'ease') {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ${name} {
                ${keyframes}
            }
            .${name} {
                animation: ${name} ${duration}ms ${easing};
            }
        `;
        document.head.appendChild(style);
        
        return name;
    }

    // 移除动画类
    removeAnimationClass(element, className, delay = 0) {
        setTimeout(() => {
            element.classList.remove(className);
        }, delay);
    }

    // 性能监控
    measureAnimationPerformance(animationName, animationFunction) {
        if (!this.enabled) return animationFunction();

        const startTime = performance.now();
        
        const result = animationFunction();
        
        if (result instanceof Promise) {
            return result.then(() => {
                const endTime = performance.now();
                console.log(`动画 ${animationName} 耗时: ${(endTime - startTime).toFixed(2)}ms`);
            });
        } else {
            const endTime = performance.now();
            console.log(`动画 ${animationName} 耗时: ${(endTime - startTime).toFixed(2)}ms`);
            return result;
        }
    }

    // 检测动画支持
    checkAnimationSupport() {
        const testElement = document.createElement('div');
        const animationSupport = {
            css: 'animation' in testElement.style,
            transform: 'transform' in testElement.style,
            transition: 'transition' in testElement.style,
            webAnimations: 'animate' in testElement
        };
        
        return animationSupport;
    }
}

// 创建全局动画管理器实例
window.animationManager = new AnimationManager();
