// 粒子系统文件
class ParticleSystem {
    constructor() {
        this.particles = [];
        this.container = null;
        this.enabled = true;
        this.maxParticles = 100;
        this.init();
    }

    init() {
        this.container = document.getElementById('particles');
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'particles';
            this.container.className = 'particles';
            document.body.appendChild(this.container);
        }

        // 绑定设置
        this.bindSettings();
        
        // 启动更新循环
        this.startUpdateLoop();
    }

    bindSettings() {
        const settings = storage.get('settings', {});
        this.enabled = settings.particles !== false;
    }

    startUpdateLoop() {
        const update = () => {
            this.update();
            requestAnimationFrame(update);
        };
        requestAnimationFrame(update);
    }

    update() {
        if (!this.enabled) return;

        // 更新所有粒子
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            this.updateParticle(particle);

            // 移除过期粒子
            if (particle.life <= 0) {
                this.removeParticle(i);
            }
        }
    }

    updateParticle(particle) {
        // 更新位置
        particle.x += particle.vx;
        particle.y += particle.vy;

        // 应用重力
        particle.vy += particle.gravity;

        // 应用阻力
        particle.vx *= particle.friction;
        particle.vy *= particle.friction;

        // 更新生命值
        particle.life -= particle.decay;

        // 更新透明度
        particle.opacity = particle.life / particle.maxLife;

        // 更新大小
        if (particle.shrink) {
            particle.size *= 0.98;
        }

        // 更新DOM元素
        if (particle.element) {
            particle.element.style.left = particle.x + 'px';
            particle.element.style.top = particle.y + 'px';
            particle.element.style.opacity = particle.opacity;
            particle.element.style.transform = `scale(${particle.size / particle.originalSize})`;
        }
    }

    removeParticle(index) {
        const particle = this.particles[index];
        if (particle.element && particle.element.parentNode) {
            particle.element.parentNode.removeChild(particle.element);
        }
        this.particles.splice(index, 1);
    }

    createParticle(options = {}) {
        if (!this.enabled || this.particles.length >= this.maxParticles) return null;

        const particle = {
            x: options.x || 0,
            y: options.y || 0,
            vx: options.vx || (Math.random() - 0.5) * 4,
            vy: options.vy || (Math.random() - 0.5) * 4,
            size: options.size || 4,
            originalSize: options.size || 4,
            color: options.color || '#776e65',
            life: options.life || 1,
            maxLife: options.life || 1,
            decay: options.decay || 0.02,
            gravity: options.gravity || 0.1,
            friction: options.friction || 0.98,
            opacity: 1,
            shrink: options.shrink || false,
            type: options.type || 'circle'
        };

        // 创建DOM元素
        particle.element = this.createElement(particle);
        this.container.appendChild(particle.element);

        this.particles.push(particle);
        return particle;
    }

    createElement(particle) {
        const element = document.createElement('div');
        element.className = 'particle';
        element.style.position = 'absolute';
        element.style.left = particle.x + 'px';
        element.style.top = particle.y + 'px';
        element.style.width = particle.size + 'px';
        element.style.height = particle.size + 'px';
        element.style.backgroundColor = particle.color;
        element.style.pointerEvents = 'none';
        element.style.zIndex = '50';

        switch (particle.type) {
            case 'circle':
                element.style.borderRadius = '50%';
                break;
            case 'square':
                element.style.borderRadius = '0';
                break;
            case 'star':
                element.innerHTML = '★';
                element.style.backgroundColor = 'transparent';
                element.style.color = particle.color;
                element.style.fontSize = particle.size + 'px';
                element.style.textAlign = 'center';
                element.style.lineHeight = particle.size + 'px';
                break;
            case 'heart':
                element.innerHTML = '♥';
                element.style.backgroundColor = 'transparent';
                element.style.color = particle.color;
                element.style.fontSize = particle.size + 'px';
                element.style.textAlign = 'center';
                element.style.lineHeight = particle.size + 'px';
                break;
        }

        return element;
    }

    // 预设粒子效果
    explode(x, y, options = {}) {
        const count = options.count || 20;
        const colors = options.colors || ['#f67c5f', '#f59563', '#f2b179', '#edcf72'];
        
        for (let i = 0; i < count; i++) {
            const angle = (Math.PI * 2 * i) / count;
            const speed = options.speed || 3;
            
            this.createParticle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed * (0.5 + Math.random() * 0.5),
                vy: Math.sin(angle) * speed * (0.5 + Math.random() * 0.5),
                size: options.size || Utils.random(3, 8),
                color: colors[Math.floor(Math.random() * colors.length)],
                life: options.life || 1,
                decay: options.decay || 0.015,
                gravity: options.gravity || 0.05,
                shrink: true
            });
        }
    }

    fireworks(x, y, options = {}) {
        const count = options.count || 30;
        const colors = options.colors || ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
        
        for (let i = 0; i < count; i++) {
            const angle = Math.random() * Math.PI * 2;
            const speed = Math.random() * 6 + 2;
            
            this.createParticle({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: Utils.random(2, 6),
                color: colors[Math.floor(Math.random() * colors.length)],
                life: 1.5,
                decay: 0.01,
                gravity: 0.02,
                type: 'star'
            });
        }
    }

    confetti(x, y, options = {}) {
        const count = options.count || 15;
        const colors = options.colors || ['#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43'];
        
        for (let i = 0; i < count; i++) {
            this.createParticle({
                x: x + Utils.random(-20, 20),
                y: y + Utils.random(-20, 20),
                vx: Utils.random(-3, 3),
                vy: Utils.random(-8, -2),
                size: Utils.random(4, 10),
                color: colors[Math.floor(Math.random() * colors.length)],
                life: 2,
                decay: 0.008,
                gravity: 0.15,
                friction: 0.99,
                type: 'square'
            });
        }
    }

    hearts(x, y, options = {}) {
        const count = options.count || 8;
        
        for (let i = 0; i < count; i++) {
            this.createParticle({
                x: x + Utils.random(-10, 10),
                y: y,
                vx: Utils.random(-1, 1),
                vy: Utils.random(-3, -1),
                size: Utils.random(12, 20),
                color: '#ff6b6b',
                life: 2,
                decay: 0.01,
                gravity: -0.02,
                friction: 0.99,
                type: 'heart'
            });
        }
    }

    scoreFloat(x, y, score, options = {}) {
        const element = document.createElement('div');
        element.textContent = '+' + score;
        element.style.position = 'absolute';
        element.style.left = x + 'px';
        element.style.top = y + 'px';
        element.style.color = options.color || '#f67c5f';
        element.style.fontSize = options.fontSize || '24px';
        element.style.fontWeight = 'bold';
        element.style.pointerEvents = 'none';
        element.style.zIndex = '100';
        element.style.animation = 'floatingScore 1s ease-out forwards';
        
        this.container.appendChild(element);
        
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 1000);
    }

    tileTrail(fromX, fromY, toX, toY, options = {}) {
        const distance = Utils.getDistance(fromX, fromY, toX, toY);
        const steps = Math.floor(distance / 10);
        
        for (let i = 0; i < steps; i++) {
            setTimeout(() => {
                const progress = i / steps;
                const x = Utils.lerp(fromX, toX, progress);
                const y = Utils.lerp(fromY, toY, progress);
                
                this.createParticle({
                    x: x,
                    y: y,
                    vx: 0,
                    vy: 0,
                    size: 3,
                    color: options.color || '#bbada0',
                    life: 0.5,
                    decay: 0.05,
                    gravity: 0,
                    friction: 1
                });
            }, i * 20);
        }
    }

    // 主题相关粒子效果
    themeParticles(theme) {
        switch (theme) {
            case 'neon':
                this.createNeonParticles();
                break;
            case 'nature':
                this.createNatureParticles();
                break;
            case 'ocean':
                this.createOceanParticles();
                break;
            case 'rainbow':
                this.createRainbowParticles();
                break;
        }
    }

    createNeonParticles() {
        const colors = ['#00ffff', '#ff006e', '#8338ec', '#3a86ff'];
        
        for (let i = 0; i < 5; i++) {
            setTimeout(() => {
                this.createParticle({
                    x: Math.random() * window.innerWidth,
                    y: window.innerHeight,
                    vx: (Math.random() - 0.5) * 2,
                    vy: -Math.random() * 3 - 1,
                    size: Utils.random(2, 4),
                    color: colors[Math.floor(Math.random() * colors.length)],
                    life: 3,
                    decay: 0.005,
                    gravity: -0.01
                });
            }, i * 200);
        }
    }

    createNatureParticles() {
        const colors = ['#8bc34a', '#4caf50', '#2e7d32'];
        
        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                this.createParticle({
                    x: Math.random() * window.innerWidth,
                    y: -10,
                    vx: (Math.random() - 0.5) * 1,
                    vy: Math.random() * 2 + 1,
                    size: Utils.random(3, 6),
                    color: colors[Math.floor(Math.random() * colors.length)],
                    life: 4,
                    decay: 0.003,
                    gravity: 0.01,
                    type: 'circle'
                });
            }, i * 300);
        }
    }

    createOceanParticles() {
        const colors = ['#03a9f4', '#0288d1', '#0277bd'];
        
        for (let i = 0; i < 6; i++) {
            setTimeout(() => {
                this.createParticle({
                    x: Math.random() * window.innerWidth,
                    y: window.innerHeight + 10,
                    vx: (Math.random() - 0.5) * 3,
                    vy: -Math.random() * 4 - 2,
                    size: Utils.random(4, 8),
                    color: colors[Math.floor(Math.random() * colors.length)],
                    life: 2.5,
                    decay: 0.008,
                    gravity: 0.05,
                    friction: 0.98
                });
            }, i * 250);
        }
    }

    createRainbowParticles() {
        const colors = ['#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43', '#ee5a24'];
        
        for (let i = 0; i < 10; i++) {
            setTimeout(() => {
                this.createParticle({
                    x: Math.random() * window.innerWidth,
                    y: Math.random() * window.innerHeight,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    size: Utils.random(3, 7),
                    color: colors[i % colors.length],
                    life: 3,
                    decay: 0.006,
                    gravity: 0,
                    friction: 0.99,
                    type: 'star'
                });
            }, i * 100);
        }
    }

    // 清除所有粒子
    clear() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            this.removeParticle(i);
        }
    }

    // 设置启用状态
    setEnabled(enabled) {
        this.enabled = enabled;
        
        if (!enabled) {
            this.clear();
        }

        // 保存设置
        const settings = storage.get('settings', {});
        settings.particles = enabled;
        storage.set('settings', settings);
    }

    // 获取启用状态
    isEnabled() {
        return this.enabled;
    }

    // 设置最大粒子数
    setMaxParticles(max) {
        this.maxParticles = max;
        
        // 如果当前粒子数超过限制，移除多余的
        while (this.particles.length > this.maxParticles) {
            this.removeParticle(this.particles.length - 1);
        }
    }
}

// 创建全局粒子系统实例
window.particleSystem = new ParticleSystem();
