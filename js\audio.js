// 音频管理文件
class AudioManager {
    constructor() {
        this.sounds = {};
        this.enabled = true;
        this.volume = 0.5;
        this.audioContext = null;
        this.init();
    }

    init() {
        // 检查音频支持
        this.checkSupport();
        
        // 初始化音频上下文
        this.initAudioContext();
        
        // 加载音效
        this.loadSounds();
        
        // 绑定设置
        this.bindSettings();
    }

    checkSupport() {
        this.isSupported = !!window.Audio;
        if (!this.isSupported) {
            console.warn('音频不支持');
        }
    }

    initAudioContext() {
        try {
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            if (window.AudioContext) {
                this.audioContext = new AudioContext();
            }
        } catch (e) {
            console.warn('Web Audio API 不支持');
        }
    }

    loadSounds() {
        // 定义音效文件
        const soundFiles = {
            move: this.createBeepSound(220, 0.1), // 移动音效
            merge: this.createBeepSound(440, 0.2), // 合并音效
            win: this.createMelody([523, 659, 784, 1047], 0.5), // 胜利音效
            gameOver: this.createBeepSound(110, 0.8), // 游戏结束音效
            newTile: this.createBeepSound(330, 0.1), // 新瓦片音效
            achievement: this.createMelody([523, 659, 784], 0.3), // 成就音效
            button: this.createBeepSound(880, 0.05), // 按钮音效
            error: this.createBeepSound(150, 0.3), // 错误音效
            tick: this.createBeepSound(1000, 0.02), // 计时器音效
            whoosh: this.createNoiseSound(0.1) // 滑动音效
        };

        // 加载音效
        Object.keys(soundFiles).forEach(key => {
            this.sounds[key] = soundFiles[key];
        });

        // 尝试加载外部音频文件
        this.loadExternalSounds();
    }

    loadExternalSounds() {
        const externalSounds = {
            move: 'sounds/move.mp3',
            merge: 'sounds/merge.mp3',
            win: 'sounds/win.mp3',
            gameOver: 'sounds/gameover.mp3'
        };

        Object.keys(externalSounds).forEach(key => {
            const audio = new Audio();
            audio.preload = 'auto';
            audio.volume = this.volume;
            
            audio.addEventListener('canplaythrough', () => {
                this.sounds[key] = audio;
            });
            
            audio.addEventListener('error', () => {
                console.warn(`音频文件加载失败: ${externalSounds[key]}`);
            });
            
            audio.src = externalSounds[key];
        });
    }

    bindSettings() {
        // 从存储中读取设置
        const settings = storage.get('settings', {});
        this.enabled = settings.sound !== false;
        this.volume = settings.volume || 0.5;
    }

    // 创建蜂鸣音效
    createBeepSound(frequency, duration) {
        if (!this.audioContext) return null;

        return () => {
            if (!this.enabled) return;

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(this.volume * 0.3, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        };
    }

    // 创建旋律音效
    createMelody(frequencies, noteDuration) {
        if (!this.audioContext) return null;

        return () => {
            if (!this.enabled) return;

            frequencies.forEach((frequency, index) => {
                setTimeout(() => {
                    const oscillator = this.audioContext.createOscillator();
                    const gainNode = this.audioContext.createGain();

                    oscillator.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);

                    oscillator.frequency.value = frequency;
                    oscillator.type = 'sine';

                    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                    gainNode.gain.linearRampToValueAtTime(this.volume * 0.2, this.audioContext.currentTime + 0.01);
                    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + noteDuration);

                    oscillator.start(this.audioContext.currentTime);
                    oscillator.stop(this.audioContext.currentTime + noteDuration);
                }, index * noteDuration * 1000);
            });
        };
    }

    // 创建噪音音效
    createNoiseSound(duration) {
        if (!this.audioContext) return null;

        return () => {
            if (!this.enabled) return;

            const bufferSize = this.audioContext.sampleRate * duration;
            const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
            const output = buffer.getChannelData(0);

            for (let i = 0; i < bufferSize; i++) {
                output[i] = Math.random() * 2 - 1;
            }

            const source = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();

            source.buffer = buffer;
            filter.type = 'highpass';
            filter.frequency.value = 1000;

            source.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(this.volume * 0.1, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

            source.start(this.audioContext.currentTime);
        };
    }

    // 播放音效
    play(soundName, options = {}) {
        if (!this.enabled || !this.sounds[soundName]) return;

        try {
            const sound = this.sounds[soundName];
            
            if (typeof sound === 'function') {
                // 程序生成的音效
                sound();
            } else if (sound instanceof Audio) {
                // 音频文件
                sound.currentTime = 0;
                sound.volume = (options.volume || 1) * this.volume;
                
                const playPromise = sound.play();
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.warn('音频播放失败:', error);
                    });
                }
            }
        } catch (error) {
            console.warn('音效播放错误:', error);
        }
    }

    // 停止所有音效
    stopAll() {
        Object.values(this.sounds).forEach(sound => {
            if (sound instanceof Audio) {
                sound.pause();
                sound.currentTime = 0;
            }
        });
    }

    // 设置音量
    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        
        // 更新所有音频元素的音量
        Object.values(this.sounds).forEach(sound => {
            if (sound instanceof Audio) {
                sound.volume = this.volume;
            }
        });

        // 保存设置
        const settings = storage.get('settings', {});
        settings.volume = this.volume;
        storage.set('settings', settings);
    }

    // 启用/禁用音效
    setEnabled(enabled) {
        this.enabled = enabled;
        
        if (!enabled) {
            this.stopAll();
        }

        // 保存设置
        const settings = storage.get('settings', {});
        settings.sound = enabled;
        storage.set('settings', settings);
    }

    // 获取音量
    getVolume() {
        return this.volume;
    }

    // 检查是否启用
    isEnabled() {
        return this.enabled;
    }

    // 预加载音效
    preload() {
        Object.values(this.sounds).forEach(sound => {
            if (sound instanceof Audio) {
                sound.load();
            }
        });
    }

    // 解锁音频上下文（用于移动设备）
    unlock() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume().then(() => {
                console.log('音频上下文已解锁');
            });
        }
    }

    // 创建音频可视化
    createVisualizer(canvas) {
        if (!this.audioContext) return null;

        const analyser = this.audioContext.createAnalyser();
        analyser.fftSize = 256;
        
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        const draw = () => {
            requestAnimationFrame(draw);
            
            analyser.getByteFrequencyData(dataArray);
            
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, width, height);
            
            const barWidth = (width / bufferLength) * 2.5;
            let barHeight;
            let x = 0;
            
            for (let i = 0; i < bufferLength; i++) {
                barHeight = dataArray[i] / 255 * height;
                
                const r = barHeight + 25 * (i / bufferLength);
                const g = 250 * (i / bufferLength);
                const b = 50;
                
                ctx.fillStyle = `rgb(${r},${g},${b})`;
                ctx.fillRect(x, height - barHeight, barWidth, barHeight);
                
                x += barWidth + 1;
            }
        };
        
        draw();
        return analyser;
    }

    // 添加音频效果
    addEffect(type, options = {}) {
        if (!this.audioContext) return null;

        let effect;
        
        switch (type) {
            case 'reverb':
                effect = this.createReverb(options.roomSize || 0.5, options.decay || 2);
                break;
            case 'delay':
                effect = this.createDelay(options.time || 0.3, options.feedback || 0.3);
                break;
            case 'distortion':
                effect = this.createDistortion(options.amount || 50);
                break;
            default:
                return null;
        }
        
        return effect;
    }

    createReverb(roomSize, decay) {
        const convolver = this.audioContext.createConvolver();
        const length = this.audioContext.sampleRate * decay;
        const impulse = this.audioContext.createBuffer(2, length, this.audioContext.sampleRate);
        
        for (let channel = 0; channel < 2; channel++) {
            const channelData = impulse.getChannelData(channel);
            for (let i = 0; i < length; i++) {
                channelData[i] = (Math.random() * 2 - 1) * Math.pow(1 - i / length, roomSize);
            }
        }
        
        convolver.buffer = impulse;
        return convolver;
    }

    createDelay(time, feedback) {
        const delay = this.audioContext.createDelay();
        const feedbackGain = this.audioContext.createGain();
        
        delay.delayTime.value = time;
        feedbackGain.gain.value = feedback;
        
        delay.connect(feedbackGain);
        feedbackGain.connect(delay);
        
        return { input: delay, output: delay };
    }

    createDistortion(amount) {
        const waveshaper = this.audioContext.createWaveShaper();
        const samples = 44100;
        const curve = new Float32Array(samples);
        const deg = Math.PI / 180;
        
        for (let i = 0; i < samples; i++) {
            const x = (i * 2) / samples - 1;
            curve[i] = ((3 + amount) * x * 20 * deg) / (Math.PI + amount * Math.abs(x));
        }
        
        waveshaper.curve = curve;
        waveshaper.oversample = '4x';
        
        return waveshaper;
    }
}

// 创建全局音频管理器实例
window.audioManager = new AudioManager();
