// UI管理文件
class UIManager {
    constructor() {
        this.game = null;
        this.gameBoard = null;
        this.isAnimating = false;
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.minSwipeDistance = 50;
        this.init();
    }

    init() {
        this.gameBoard = document.getElementById('gameBoard');
        this.bindEvents();
        this.initializeUI();
    }

    setGame(game) {
        this.game = game;
        this.updateBoard();
    }

    bindEvents() {
        // 键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        
        // 触摸事件
        if (Utils.isTouchDevice()) {
            this.gameBoard.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
            this.gameBoard.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        }
        
        // 鼠标事件（用于桌面拖拽）
        this.gameBoard.addEventListener('mousedown', this.handleMouseDown.bind(this));
        
        // 按钮事件
        this.bindButtonEvents();
        
        // 窗口事件
        window.addEventListener('resize', Utils.debounce(this.handleResize.bind(this), 250));
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
        
        // 可见性变化事件
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }

    bindButtonEvents() {
        // 新游戏按钮
        const newGameBtn = document.getElementById('newGameBtn');
        if (newGameBtn) {
            newGameBtn.addEventListener('click', () => {
                this.showConfirmDialog('开始新游戏？', '当前进度将丢失', () => {
                    this.game.restart();
                    this.updateBoard();
                });
            });
        }

        // 撤销按钮
        const undoBtn = document.getElementById('undoBtn');
        if (undoBtn) {
            undoBtn.addEventListener('click', () => {
                if (this.game.undo()) {
                    this.updateBoard();
                }
            });
        }

        // 提示按钮
        const hintBtn = document.getElementById('hintBtn');
        if (hintBtn) {
            hintBtn.addEventListener('click', () => {
                const hint = this.game.getHint();
                if (hint) {
                    this.showHint(hint);
                } else {
                    this.showToast('没有可用的提示');
                }
            });
        }

        // 暂停按钮
        const pauseBtn = document.getElementById('pauseBtn');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => {
                const isPaused = this.game.pause();
                pauseBtn.textContent = isPaused ? '继续' : '暂停';
                if (isPaused) {
                    this.showPauseOverlay();
                } else {
                    this.hidePauseOverlay();
                }
            });
        }

        // 菜单按钮
        const menuBtn = document.getElementById('menuBtn');
        if (menuBtn) {
            menuBtn.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleFullscreen();
            });
        }

        // 主题按钮
        const themeBtn = document.getElementById('themeBtn');
        if (themeBtn) {
            themeBtn.addEventListener('click', () => {
                this.toggleThemeSelector();
            });
        }

        // 游戏模式选择
        const gameMode = document.getElementById('gameMode');
        if (gameMode) {
            gameMode.addEventListener('change', (e) => {
                this.changeGameSize(parseInt(e.target.value));
            });
        }

        // 游戏类型选择
        const gameType = document.getElementById('gameType');
        if (gameType) {
            gameType.addEventListener('change', (e) => {
                this.changeGameType(e.target.value);
            });
        }

        // 重新开始按钮（游戏结束界面）
        const restartBtn = document.getElementById('restartBtn');
        if (restartBtn) {
            restartBtn.addEventListener('click', () => {
                this.game.restart();
                this.updateBoard();
            });
        }

        // 分享按钮
        const shareBtn = document.getElementById('shareBtn');
        if (shareBtn) {
            shareBtn.addEventListener('click', () => {
                this.shareGame();
            });
        }
    }

    handleKeyDown(e) {
        if (this.isAnimating || this.game.isGameOver || this.game.isPaused) return;

        const keyMap = {
            'ArrowLeft': 'left',
            'ArrowRight': 'right',
            'ArrowUp': 'up',
            'ArrowDown': 'down',
            'KeyA': 'left',
            'KeyD': 'right',
            'KeyW': 'up',
            'KeyS': 'down'
        };

        const direction = keyMap[e.code];
        if (direction) {
            e.preventDefault();
            this.makeMove(direction);
        }

        // 其他快捷键
        if (e.code === 'KeyZ' && e.ctrlKey) {
            e.preventDefault();
            if (this.game.undo()) {
                this.updateBoard();
            }
        }

        if (e.code === 'KeyR' && e.ctrlKey) {
            e.preventDefault();
            this.game.restart();
            this.updateBoard();
        }

        if (e.code === 'Space') {
            e.preventDefault();
            this.game.pause();
        }
    }

    handleTouchStart(e) {
        e.preventDefault();
        const touch = e.touches[0];
        this.touchStartX = touch.clientX;
        this.touchStartY = touch.clientY;
    }

    handleTouchEnd(e) {
        e.preventDefault();
        if (!e.changedTouches[0]) return;

        const touch = e.changedTouches[0];
        const deltaX = touch.clientX - this.touchStartX;
        const deltaY = touch.clientY - this.touchStartY;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        if (Math.max(absDeltaX, absDeltaY) < this.minSwipeDistance) return;

        let direction;
        if (absDeltaX > absDeltaY) {
            direction = deltaX > 0 ? 'right' : 'left';
        } else {
            direction = deltaY > 0 ? 'down' : 'up';
        }

        this.makeMove(direction);
    }

    handleMouseDown(e) {
        if (Utils.isTouchDevice()) return;

        const startX = e.clientX;
        const startY = e.clientY;

        const handleMouseMove = (e) => {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            const absDeltaX = Math.abs(deltaX);
            const absDeltaY = Math.abs(deltaY);

            if (Math.max(absDeltaX, absDeltaY) < this.minSwipeDistance) return;

            let direction;
            if (absDeltaX > absDeltaY) {
                direction = deltaX > 0 ? 'right' : 'left';
            } else {
                direction = deltaY > 0 ? 'down' : 'up';
            }

            this.makeMove(direction);
            cleanup();
        };

        const handleMouseUp = () => {
            cleanup();
        };

        const cleanup = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    }

    makeMove(direction) {
        if (this.isAnimating) return;

        this.isAnimating = true;
        
        if (this.game.move(direction)) {
            this.animateMove(direction, () => {
                this.updateBoard();
                this.isAnimating = false;
            });
        } else {
            // 无效移动，播放震动效果
            this.shakeBoard();
            this.isAnimating = false;
        }
    }

    animateMove(direction, callback) {
        // 添加移动动画
        this.gameBoard.classList.add('animate-move');
        
        setTimeout(() => {
            this.gameBoard.classList.remove('animate-move');
            if (callback) callback();
        }, 150);
    }

    shakeBoard() {
        this.gameBoard.classList.add('animate-shake');
        setTimeout(() => {
            this.gameBoard.classList.remove('animate-shake');
        }, 500);
    }

    updateBoard() {
        if (!this.game || !this.gameBoard) return;

        // 清空游戏板
        this.gameBoard.innerHTML = '';
        
        // 设置游戏板大小
        this.gameBoard.className = `game-board size-${this.game.size}`;
        
        // 创建瓦片
        for (let i = 0; i < this.game.size; i++) {
            for (let j = 0; j < this.game.size; j++) {
                const value = this.game.board[i][j];
                const tile = this.createTile(value, i, j);
                this.gameBoard.appendChild(tile);
            }
        }

        // 更新分数显示
        this.game.updateScore();
        
        // 更新按钮状态
        this.updateButtonStates();
    }

    createTile(value, row, col) {
        const tile = document.createElement('div');
        tile.className = 'tile';
        tile.dataset.row = row;
        tile.dataset.col = col;
        
        if (value > 0) {
            tile.classList.add(`tile-${value}`);
            tile.textContent = value;
            
            // 添加出现动画
            if (this.isNewTile(row, col)) {
                tile.classList.add('new-tile');
            }
        }
        
        return tile;
    }

    isNewTile(row, col) {
        // 简单检查：如果这是最近添加的瓦片
        // 实际实现中可能需要更复杂的逻辑
        return false;
    }

    updateButtonStates() {
        const undoBtn = document.getElementById('undoBtn');
        if (undoBtn) {
            undoBtn.disabled = !this.game.canUndo;
        }

        const pauseBtn = document.getElementById('pauseBtn');
        if (pauseBtn) {
            pauseBtn.textContent = this.game.isPaused ? '继续' : '暂停';
        }
    }

    changeGameSize(size) {
        this.showConfirmDialog('改变游戏大小？', '当前进度将丢失', () => {
            this.game.size = size;
            this.game.restart();
            this.updateBoard();
            
            // 保存偏好设置
            const preferences = storage.get('preferences', {});
            preferences.boardSize = size;
            storage.set('preferences', preferences);
        });
    }

    changeGameType(type) {
        this.game.gameMode = type;
        
        // 根据游戏类型调整设置
        switch (type) {
            case 'time':
                this.startTimeChallenge();
                break;
            case 'endless':
                this.game.targetTile = Infinity;
                break;
            case 'target':
                this.showTargetDialog();
                break;
        }
        
        // 保存偏好设置
        const preferences = storage.get('preferences', {});
        preferences.gameMode = type;
        storage.set('preferences', preferences);
    }

    startTimeChallenge() {
        // 实现时间挑战模式
        this.showToast('时间挑战模式：在3分钟内达到最高分！');
        
        setTimeout(() => {
            if (this.game.gameMode === 'time' && !this.game.isGameOver) {
                this.game.isGameOver = true;
                this.game.handleGameOver();
            }
        }, 180000); // 3分钟
    }

    showTargetDialog() {
        const target = prompt('请输入目标数字:', '4096');
        if (target && !isNaN(target)) {
            this.game.targetTile = parseInt(target);
            
            // 保存偏好设置
            const preferences = storage.get('preferences', {});
            preferences.targetTile = this.game.targetTile;
            storage.set('preferences', preferences);
        }
    }

    showHint(direction) {
        const directionMap = {
            'left': '←',
            'right': '→',
            'up': '↑',
            'down': '↓'
        };
        
        this.showToast(`提示：向${directionMap[direction]}移动`);
        
        // 高亮提示方向
        this.highlightDirection(direction);
    }

    highlightDirection(direction) {
        // 创建方向指示器
        const indicator = document.createElement('div');
        indicator.className = 'direction-indicator';
        indicator.textContent = direction === 'left' ? '←' : 
                               direction === 'right' ? '→' : 
                               direction === 'up' ? '↑' : '↓';
        
        indicator.style.position = 'absolute';
        indicator.style.fontSize = '48px';
        indicator.style.color = '#f67c5f';
        indicator.style.zIndex = '200';
        indicator.style.animation = 'hintPulse 2s ease-in-out';
        
        // 定位指示器
        const boardRect = this.gameBoard.getBoundingClientRect();
        indicator.style.left = (boardRect.left + boardRect.width / 2 - 24) + 'px';
        indicator.style.top = (boardRect.top + boardRect.height / 2 - 24) + 'px';
        
        document.body.appendChild(indicator);
        
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 2000);
    }

    showToast(message, duration = 3000) {
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            z-index: 1000;
            animation: overlaySlideIn 0.3s ease-in-out;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.animation = 'overlaySlideIn 0.3s ease-in-out reverse';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, duration);
    }

    showConfirmDialog(title, message, onConfirm) {
        if (confirm(`${title}\n${message}`)) {
            onConfirm();
        }
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('hidden');
            sidebar.classList.toggle('show');
        }
    }

    toggleThemeSelector() {
        const themeSelector = document.getElementById('themeSelector');
        if (themeSelector) {
            themeSelector.classList.toggle('hidden');
            if (!themeSelector.classList.contains('hidden')) {
                themeSelector.classList.add('animate-theme-pop');
            }
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    shareGame() {
        const shareText = `我在超级2048中得到了${this.game.score}分！最高瓦片：${this.game.getHighestTile()}`;
        
        if (navigator.share) {
            navigator.share({
                title: '超级2048',
                text: shareText,
                url: window.location.href
            });
        } else {
            Utils.copyToClipboard(shareText).then(success => {
                if (success) {
                    this.showToast('分享内容已复制到剪贴板！');
                } else {
                    this.showToast('分享失败');
                }
            });
        }
    }

    showPauseOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'pauseOverlay';
        overlay.className = 'game-overlay';
        overlay.innerHTML = `
            <div class="overlay-content">
                <h2>游戏暂停</h2>
                <p>点击继续按钮恢复游戏</p>
            </div>
        `;
        
        this.gameBoard.parentNode.appendChild(overlay);
    }

    hidePauseOverlay() {
        const overlay = document.getElementById('pauseOverlay');
        if (overlay) {
            overlay.parentNode.removeChild(overlay);
        }
    }

    handleResize() {
        // 处理窗口大小变化
        this.updateBoard();
    }

    handleBeforeUnload(e) {
        if (!this.game.isGameOver && this.game.moves > 0) {
            e.preventDefault();
            e.returnValue = '游戏正在进行中，确定要离开吗？';
        }
    }

    handleVisibilityChange() {
        if (document.hidden && !this.game.isGameOver) {
            this.game.pause();
        }
    }

    initializeUI() {
        // 初始化UI状态
        this.updateThemeFromStorage();
        this.updateSettingsFromStorage();
    }

    updateThemeFromStorage() {
        const settings = storage.get('settings', {});
        const theme = settings.theme || 'classic';
        document.body.className = `theme-${theme}`;
    }

    updateSettingsFromStorage() {
        const settings = storage.get('settings', {});
        
        // 更新设置控件状态
        const soundToggle = document.getElementById('soundToggle');
        if (soundToggle) {
            soundToggle.checked = settings.sound !== false;
        }
        
        const animationToggle = document.getElementById('animationToggle');
        if (animationToggle) {
            animationToggle.checked = settings.animation !== false;
        }
        
        const particleToggle = document.getElementById('particleToggle');
        if (particleToggle) {
            particleToggle.checked = settings.particles !== false;
        }
        
        const autoSaveToggle = document.getElementById('autoSaveToggle');
        if (autoSaveToggle) {
            autoSaveToggle.checked = settings.autoSave !== false;
        }
    }
}

// 导出UI管理器类
window.UIManager = UIManager;
