// 成就系统文件
class AchievementSystem {
    constructor() {
        this.achievements = {};
        this.init();
    }

    init() {
        this.defineAchievements();
        this.loadUnlockedAchievements();
    }

    defineAchievements() {
        this.achievements = {
            // 基础成就
            'first_game': {
                id: 'first_game',
                title: '初次尝试',
                description: '完成第一局游戏',
                icon: '🎮',
                condition: (game, stats) => stats.gamesPlayed >= 1
            },
            
            'reach_128': {
                id: 'reach_128',
                title: '小有成就',
                description: '达到128瓦片',
                icon: '🏆',
                condition: (game, stats) => stats.highestTile >= 128
            },
            
            'reach_256': {
                id: 'reach_256',
                title: '渐入佳境',
                description: '达到256瓦片',
                icon: '🥉',
                condition: (game, stats) => stats.highestTile >= 256
            },
            
            'reach_512': {
                id: 'reach_512',
                title: '技艺精湛',
                description: '达到512瓦片',
                icon: '🥈',
                condition: (game, stats) => stats.highestTile >= 512
            },
            
            'reach_1024': {
                id: 'reach_1024',
                title: '大师级别',
                description: '达到1024瓦片',
                icon: '🥇',
                condition: (game, stats) => stats.highestTile >= 1024
            },
            
            'reach_2048': {
                id: 'reach_2048',
                title: '传奇玩家',
                description: '达到2048瓦片',
                icon: '👑',
                condition: (game, stats) => stats.highestTile >= 2048
            },
            
            'reach_4096': {
                id: 'reach_4096',
                title: '超越极限',
                description: '达到4096瓦片',
                icon: '🌟',
                condition: (game, stats) => stats.highestTile >= 4096
            },
            
            // 分数成就
            'score_1000': {
                id: 'score_1000',
                title: '千分达人',
                description: '单局得分超过1000',
                icon: '💯',
                condition: (game, stats) => game.score >= 1000
            },
            
            'score_10000': {
                id: 'score_10000',
                title: '万分高手',
                description: '单局得分超过10000',
                icon: '💎',
                condition: (game, stats) => game.score >= 10000
            },
            
            'score_50000': {
                id: 'score_50000',
                title: '五万传说',
                description: '单局得分超过50000',
                icon: '🔥',
                condition: (game, stats) => game.score >= 50000
            },
            
            // 移动次数成就
            'efficient_player': {
                id: 'efficient_player',
                title: '效率专家',
                description: '用少于200步达到1024',
                icon: '⚡',
                condition: (game, stats) => game.getHighestTile() >= 1024 && game.moves < 200
            },
            
            'speed_demon': {
                id: 'speed_demon',
                title: '速度恶魔',
                description: '5分钟内达到512',
                icon: '🏃',
                condition: (game, stats) => game.getHighestTile() >= 512 && game.gameTime < 300
            },
            
            // 连胜成就
            'win_streak_3': {
                id: 'win_streak_3',
                title: '三连胜',
                description: '连续获胜3局',
                icon: '🔥',
                condition: (game, stats) => stats.streakCurrent >= 3
            },
            
            'win_streak_5': {
                id: 'win_streak_5',
                title: '五连胜',
                description: '连续获胜5局',
                icon: '🌟',
                condition: (game, stats) => stats.streakCurrent >= 5
            },
            
            'win_streak_10': {
                id: 'win_streak_10',
                title: '十连胜',
                description: '连续获胜10局',
                icon: '👑',
                condition: (game, stats) => stats.streakCurrent >= 10
            },
            
            // 游戏次数成就
            'veteran_player': {
                id: 'veteran_player',
                title: '资深玩家',
                description: '完成100局游戏',
                icon: '🎖️',
                condition: (game, stats) => stats.gamesPlayed >= 100
            },
            
            'addicted_player': {
                id: 'addicted_player',
                title: '游戏达人',
                description: '完成500局游戏',
                icon: '🏅',
                condition: (game, stats) => stats.gamesPlayed >= 500
            },
            
            // 特殊成就
            'corner_master': {
                id: 'corner_master',
                title: '角落大师',
                description: '最大瓦片在角落位置获胜',
                icon: '📐',
                condition: (game, stats) => {
                    if (!game.isWon) return false;
                    const maxTile = game.getHighestTile();
                    const corners = [
                        game.board[0][0],
                        game.board[0][game.size - 1],
                        game.board[game.size - 1][0],
                        game.board[game.size - 1][game.size - 1]
                    ];
                    return corners.includes(maxTile);
                }
            },
            
            'minimalist': {
                id: 'minimalist',
                title: '极简主义',
                description: '用最少的瓦片数量获胜',
                icon: '🎯',
                condition: (game, stats) => {
                    if (!game.isWon) return false;
                    const filledCells = game.board.flat().filter(cell => cell > 0).length;
                    return filledCells <= 8;
                }
            },
            
            'perfectionist': {
                id: 'perfectionist',
                title: '完美主义者',
                description: '游戏板完全填满时获胜',
                icon: '💯',
                condition: (game, stats) => {
                    if (!game.isWon) return false;
                    const emptyCells = game.getEmptyCells().length;
                    return emptyCells === 0;
                }
            },
            
            // 时间成就
            'marathon_player': {
                id: 'marathon_player',
                title: '马拉松选手',
                description: '单局游戏时间超过1小时',
                icon: '⏰',
                condition: (game, stats) => game.gameTime >= 3600
            },
            
            'quick_winner': {
                id: 'quick_winner',
                title: '闪电获胜',
                description: '3分钟内获胜',
                icon: '⚡',
                condition: (game, stats) => game.isWon && game.gameTime <= 180
            },
            
            // 累计成就
            'total_score_100k': {
                id: 'total_score_100k',
                title: '十万分俱乐部',
                description: '累计得分超过100,000',
                icon: '💰',
                condition: (game, stats) => stats.totalScore >= 100000
            },
            
            'total_score_1m': {
                id: 'total_score_1m',
                title: '百万分传奇',
                description: '累计得分超过1,000,000',
                icon: '💎',
                condition: (game, stats) => stats.totalScore >= 1000000
            },
            
            // 特殊挑战成就
            'no_undo_winner': {
                id: 'no_undo_winner',
                title: '一次成功',
                description: '不使用撤销功能获胜',
                icon: '🎯',
                condition: (game, stats) => {
                    // 需要在游戏中跟踪是否使用了撤销
                    return game.isWon && !game.hasUsedUndo;
                }
            },
            
            'large_board_master': {
                id: 'large_board_master',
                title: '大棋盘大师',
                description: '在6x6棋盘上达到2048',
                icon: '🏰',
                condition: (game, stats) => game.size === 6 && game.getHighestTile() >= 2048
            },
            
            'small_board_expert': {
                id: 'small_board_expert',
                title: '小棋盘专家',
                description: '在3x3棋盘上达到512',
                icon: '🎲',
                condition: (game, stats) => game.size === 3 && game.getHighestTile() >= 512
            }
        };
    }

    loadUnlockedAchievements() {
        this.unlockedAchievements = storage.get('achievements', {});
    }

    checkAchievements(game) {
        const stats = storage.get('statistics', {});
        const newlyUnlocked = [];

        Object.values(this.achievements).forEach(achievement => {
            if (!this.isUnlocked(achievement.id) && achievement.condition(game, stats)) {
                this.unlockAchievement(achievement.id);
                newlyUnlocked.push(achievement);
            }
        });

        // 显示新解锁的成就
        newlyUnlocked.forEach(achievement => {
            this.showAchievementNotification(achievement);
        });

        return newlyUnlocked;
    }

    unlockAchievement(achievementId) {
        if (storage.unlockAchievement(achievementId)) {
            this.unlockedAchievements[achievementId] = {
                unlocked: true,
                timestamp: Date.now()
            };
            
            // 播放成就音效
            audioManager.play('achievement');
            
            // 创建庆祝粒子效果
            if (particleSystem.isEnabled()) {
                particleSystem.confetti(window.innerWidth / 2, window.innerHeight / 2);
            }
            
            return true;
        }
        return false;
    }

    isUnlocked(achievementId) {
        return this.unlockedAchievements[achievementId]?.unlocked || false;
    }

    getUnlockedCount() {
        return Object.keys(this.unlockedAchievements).filter(id => 
            this.unlockedAchievements[id].unlocked
        ).length;
    }

    getTotalCount() {
        return Object.keys(this.achievements).length;
    }

    getProgress() {
        return Math.round((this.getUnlockedCount() / this.getTotalCount()) * 100);
    }

    showAchievementNotification(achievement) {
        const notification = document.createElement('div');
        notification.className = 'achievement-notification';
        notification.innerHTML = `
            <div class="achievement-icon">${achievement.icon}</div>
            <div class="achievement-info">
                <div class="achievement-title">成就解锁！</div>
                <div class="achievement-name">${achievement.title}</div>
                <div class="achievement-desc">${achievement.description}</div>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            max-width: 300px;
            animation: achievementSlideIn 0.5s ease-out;
            display: flex;
            align-items: center;
            gap: 15px;
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes achievementSlideIn {
                0% {
                    transform: translateX(100%);
                    opacity: 0;
                }
                100% {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            .achievement-icon {
                font-size: 32px;
                flex-shrink: 0;
            }
            
            .achievement-title {
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 4px;
            }
            
            .achievement-name {
                font-weight: 600;
                font-size: 16px;
                margin-bottom: 2px;
            }
            
            .achievement-desc {
                font-size: 12px;
                opacity: 0.9;
            }
        `;
        
        if (!document.querySelector('#achievement-styles')) {
            style.id = 'achievement-styles';
            document.head.appendChild(style);
        }
        
        document.body.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            notification.style.animation = 'achievementSlideIn 0.5s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 500);
        }, 4000);
        
        // 点击关闭
        notification.addEventListener('click', () => {
            notification.style.animation = 'achievementSlideIn 0.3s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }

    renderAchievementsList() {
        const container = document.getElementById('achievements');
        if (!container) return;

        container.innerHTML = '';
        
        Object.values(this.achievements).forEach(achievement => {
            const isUnlocked = this.isUnlocked(achievement.id);
            const achievementElement = document.createElement('div');
            achievementElement.className = `achievement-item ${isUnlocked ? 'unlocked' : ''}`;
            
            achievementElement.innerHTML = `
                <div class="achievement-icon">${achievement.icon}</div>
                <div class="achievement-info">
                    <div class="achievement-title">${achievement.title}</div>
                    <div class="achievement-description">${achievement.description}</div>
                </div>
            `;
            
            if (isUnlocked) {
                const timestamp = this.unlockedAchievements[achievement.id].timestamp;
                const date = new Date(timestamp).toLocaleDateString();
                achievementElement.title = `解锁时间: ${date}`;
            }
            
            container.appendChild(achievementElement);
        });
        
        // 添加进度信息
        const progressElement = document.createElement('div');
        progressElement.className = 'achievements-progress';
        progressElement.innerHTML = `
            <div class="progress-text">
                成就进度: ${this.getUnlockedCount()}/${this.getTotalCount()} (${this.getProgress()}%)
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: ${this.getProgress()}%"></div>
            </div>
        `;
        
        container.insertBefore(progressElement, container.firstChild);
    }

    exportAchievements() {
        return {
            unlocked: this.unlockedAchievements,
            progress: this.getProgress(),
            total: this.getTotalCount(),
            unlockedCount: this.getUnlockedCount()
        };
    }

    importAchievements(data) {
        if (data.unlocked) {
            this.unlockedAchievements = data.unlocked;
            storage.set('achievements', this.unlockedAchievements);
            this.renderAchievementsList();
        }
    }

    resetAchievements() {
        this.unlockedAchievements = {};
        storage.set('achievements', {});
        this.renderAchievementsList();
    }

    getAchievementById(id) {
        return this.achievements[id];
    }

    getUnlockedAchievements() {
        return Object.keys(this.unlockedAchievements)
            .filter(id => this.unlockedAchievements[id].unlocked)
            .map(id => this.achievements[id])
            .filter(achievement => achievement);
    }

    getLockedAchievements() {
        return Object.values(this.achievements)
            .filter(achievement => !this.isUnlocked(achievement.id));
    }

    // 获取成就提示
    getAchievementHints(game) {
        const stats = storage.get('statistics', {});
        const hints = [];
        
        const lockedAchievements = this.getLockedAchievements();
        
        // 找到接近完成的成就
        lockedAchievements.forEach(achievement => {
            const hint = this.getAchievementHint(achievement, game, stats);
            if (hint) {
                hints.push(hint);
            }
        });
        
        return hints.slice(0, 3); // 返回最多3个提示
    }

    getAchievementHint(achievement, game, stats) {
        // 根据成就类型生成提示
        switch (achievement.id) {
            case 'reach_128':
                if (game.getHighestTile() >= 64) {
                    return `距离"${achievement.title}"还差一步！当前最高瓦片：${game.getHighestTile()}`;
                }
                break;
            case 'score_1000':
                if (game.score >= 500) {
                    return `距离"${achievement.title}"还需${1000 - game.score}分！`;
                }
                break;
            case 'efficient_player':
                if (game.getHighestTile() >= 512 && game.moves < 250) {
                    return `保持高效率！当前${game.moves}步，目标200步内达到1024`;
                }
                break;
        }
        
        return null;
    }
}

// 创建全局成就系统实例
window.achievementSystem = new AchievementSystem();
