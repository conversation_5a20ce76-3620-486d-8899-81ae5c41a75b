// 统计系统文件
class StatisticsManager {
    constructor() {
        this.stats = {};
        this.init();
    }

    init() {
        this.loadStatistics();
        this.renderStatistics();
    }

    loadStatistics() {
        this.stats = storage.get('statistics', {
            gamesPlayed: 0,
            gamesWon: 0,
            totalScore: 0,
            bestScore: 0,
            totalMoves: 0,
            totalTime: 0,
            averageScore: 0,
            winRate: 0,
            highestTile: 0,
            streakCurrent: 0,
            streakBest: 0,
            averageTime: 0,
            averageMoves: 0,
            fastestWin: 0,
            slowestWin: 0,
            mostMoves: 0,
            leastMoves: 0,
            sessionsToday: 0,
            lastPlayDate: null,
            totalDays: 0,
            favoriteSize: 4,
            favoriteMode: 'classic'
        });
    }

    updateStatistics(gameData) {
        // 基础统计更新
        this.stats.gamesPlayed++;
        this.stats.totalScore += gameData.score;
        this.stats.totalMoves += gameData.moves;
        this.stats.totalTime += gameData.gameTime;

        // 最佳分数
        if (gameData.score > this.stats.bestScore) {
            this.stats.bestScore = gameData.score;
        }

        // 最高瓦片
        const highestTile = gameData.highestTile || 0;
        if (highestTile > this.stats.highestTile) {
            this.stats.highestTile = highestTile;
        }

        // 获胜统计
        if (gameData.isWon) {
            this.stats.gamesWon++;
            
            // 最快/最慢获胜时间
            if (this.stats.fastestWin === 0 || gameData.gameTime < this.stats.fastestWin) {
                this.stats.fastestWin = gameData.gameTime;
            }
            if (gameData.gameTime > this.stats.slowestWin) {
                this.stats.slowestWin = gameData.gameTime;
            }

            // 最少/最多移动次数
            if (this.stats.leastMoves === 0 || gameData.moves < this.stats.leastMoves) {
                this.stats.leastMoves = gameData.moves;
            }
            if (gameData.moves > this.stats.mostMoves) {
                this.stats.mostMoves = gameData.moves;
            }

            // 连胜记录
            this.stats.streakCurrent++;
            if (this.stats.streakCurrent > this.stats.streakBest) {
                this.stats.streakBest = this.stats.streakCurrent;
            }
        } else {
            this.stats.streakCurrent = 0;
        }

        // 计算平均值
        this.calculateAverages();

        // 更新日期相关统计
        this.updateDateStatistics();

        // 更新偏好统计
        this.updatePreferences(gameData);

        // 保存统计数据
        this.saveStatistics();

        // 重新渲染
        this.renderStatistics();
    }

    calculateAverages() {
        if (this.stats.gamesPlayed > 0) {
            this.stats.averageScore = Math.round(this.stats.totalScore / this.stats.gamesPlayed);
            this.stats.averageTime = Math.round(this.stats.totalTime / this.stats.gamesPlayed);
            this.stats.averageMoves = Math.round(this.stats.totalMoves / this.stats.gamesPlayed);
            this.stats.winRate = Math.round((this.stats.gamesWon / this.stats.gamesPlayed) * 100);
        }
    }

    updateDateStatistics() {
        const today = new Date().toDateString();
        const lastPlayDate = this.stats.lastPlayDate;

        if (lastPlayDate !== today) {
            this.stats.lastPlayDate = today;
            this.stats.totalDays++;
            this.stats.sessionsToday = 1;
        } else {
            this.stats.sessionsToday++;
        }
    }

    updatePreferences(gameData) {
        // 更新最喜欢的棋盘大小
        const sizeKey = `size_${gameData.size}_count`;
        this.stats[sizeKey] = (this.stats[sizeKey] || 0) + 1;

        // 找出最常用的棋盘大小
        let maxCount = 0;
        let favoriteSize = 4;
        for (let size = 3; size <= 6; size++) {
            const count = this.stats[`size_${size}_count`] || 0;
            if (count > maxCount) {
                maxCount = count;
                favoriteSize = size;
            }
        }
        this.stats.favoriteSize = favoriteSize;

        // 更新最喜欢的游戏模式
        const modeKey = `mode_${gameData.gameMode}_count`;
        this.stats[modeKey] = (this.stats[modeKey] || 0) + 1;

        // 找出最常用的游戏模式
        const modes = ['classic', 'time', 'endless', 'target'];
        let maxModeCount = 0;
        let favoriteMode = 'classic';
        modes.forEach(mode => {
            const count = this.stats[`mode_${mode}_count`] || 0;
            if (count > maxModeCount) {
                maxModeCount = count;
                favoriteMode = mode;
            }
        });
        this.stats.favoriteMode = favoriteMode;
    }

    saveStatistics() {
        storage.set('statistics', this.stats);
    }

    renderStatistics() {
        const container = document.getElementById('statistics');
        if (!container) return;

        container.innerHTML = '';

        const statisticsData = [
            {
                label: '游戏次数',
                value: this.stats.gamesPlayed,
                icon: '🎮'
            },
            {
                label: '获胜次数',
                value: this.stats.gamesWon,
                icon: '🏆'
            },
            {
                label: '胜率',
                value: this.stats.winRate + '%',
                icon: '📊'
            },
            {
                label: '最高分',
                value: Utils.formatNumber(this.stats.bestScore),
                icon: '⭐'
            },
            {
                label: '平均分',
                value: Utils.formatNumber(this.stats.averageScore),
                icon: '📈'
            },
            {
                label: '总分',
                value: Utils.formatNumber(this.stats.totalScore),
                icon: '💯'
            },
            {
                label: '最高瓦片',
                value: this.stats.highestTile,
                icon: '🔥'
            },
            {
                label: '总移动次数',
                value: Utils.formatNumber(this.stats.totalMoves),
                icon: '👆'
            },
            {
                label: '平均移动',
                value: this.stats.averageMoves,
                icon: '🎯'
            },
            {
                label: '总游戏时间',
                value: Utils.formatTime(this.stats.totalTime),
                icon: '⏱️'
            },
            {
                label: '平均时间',
                value: Utils.formatTime(this.stats.averageTime),
                icon: '⏰'
            },
            {
                label: '当前连胜',
                value: this.stats.streakCurrent,
                icon: '🔥'
            },
            {
                label: '最佳连胜',
                value: this.stats.streakBest,
                icon: '🌟'
            },
            {
                label: '游戏天数',
                value: this.stats.totalDays,
                icon: '📅'
            }
        ];

        // 添加高级统计
        if (this.stats.gamesWon > 0) {
            statisticsData.push(
                {
                    label: '最快获胜',
                    value: Utils.formatTime(this.stats.fastestWin),
                    icon: '⚡'
                },
                {
                    label: '最少移动获胜',
                    value: this.stats.leastMoves,
                    icon: '🎯'
                }
            );
        }

        statisticsData.forEach(stat => {
            const statElement = document.createElement('div');
            statElement.className = 'stat-item';
            statElement.innerHTML = `
                <span class="stat-icon">${stat.icon}</span>
                <span class="stat-label">${stat.label}</span>
                <span class="stat-value">${stat.value}</span>
            `;
            container.appendChild(statElement);
        });

        // 添加图表
        this.renderCharts();
    }

    renderCharts() {
        // 创建简单的文本图表
        this.renderScoreChart();
        this.renderProgressChart();
    }

    renderScoreChart() {
        const container = document.getElementById('statistics');
        if (!container) return;

        const chartContainer = document.createElement('div');
        chartContainer.className = 'chart-container';
        chartContainer.innerHTML = `
            <h4>分数趋势</h4>
            <div class="score-chart">
                ${this.generateScoreChart()}
            </div>
        `;
        container.appendChild(chartContainer);
    }

    generateScoreChart() {
        const history = storage.getHistory(10);
        if (history.length === 0) return '<p>暂无数据</p>';

        const maxScore = Math.max(...history.map(game => game.score));
        
        return history.map((game, index) => {
            const height = (game.score / maxScore) * 100;
            const date = new Date(game.timestamp).toLocaleDateString();
            
            return `
                <div class="chart-bar" style="height: ${height}%" title="日期: ${date}, 分数: ${game.score}">
                    <div class="bar-value">${Utils.formatNumber(game.score)}</div>
                </div>
            `;
        }).join('');
    }

    renderProgressChart() {
        const container = document.getElementById('statistics');
        if (!container) return;

        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        progressContainer.innerHTML = `
            <h4>游戏进度</h4>
            <div class="progress-stats">
                ${this.generateProgressStats()}
            </div>
        `;
        container.appendChild(progressContainer);
    }

    generateProgressStats() {
        const tiles = [128, 256, 512, 1024, 2048, 4096];
        
        return tiles.map(tile => {
            const achieved = this.stats.highestTile >= tile;
            const percentage = achieved ? 100 : Math.min((this.stats.highestTile / tile) * 100, 99);
            
            return `
                <div class="progress-item">
                    <span class="progress-label">${tile}</span>
                    <div class="progress-bar">
                        <div class="progress-fill ${achieved ? 'completed' : ''}" 
                             style="width: ${percentage}%"></div>
                    </div>
                    <span class="progress-status">${achieved ? '✓' : Math.round(percentage) + '%'}</span>
                </div>
            `;
        }).join('');
    }

    getDetailedReport() {
        const report = {
            summary: {
                totalGames: this.stats.gamesPlayed,
                winRate: this.stats.winRate,
                bestScore: this.stats.bestScore,
                highestTile: this.stats.highestTile,
                totalTime: this.stats.totalTime,
                totalMoves: this.stats.totalMoves
            },
            averages: {
                scorePerGame: this.stats.averageScore,
                timePerGame: this.stats.averageTime,
                movesPerGame: this.stats.averageMoves
            },
            records: {
                bestStreak: this.stats.streakBest,
                fastestWin: this.stats.fastestWin,
                leastMoves: this.stats.leastMoves
            },
            preferences: {
                favoriteSize: this.stats.favoriteSize,
                favoriteMode: this.stats.favoriteMode
            },
            activity: {
                totalDays: this.stats.totalDays,
                sessionsToday: this.stats.sessionsToday,
                lastPlayDate: this.stats.lastPlayDate
            }
        };

        return report;
    }

    exportStatistics() {
        return {
            statistics: this.stats,
            report: this.getDetailedReport(),
            exportDate: new Date().toISOString()
        };
    }

    importStatistics(data) {
        if (data.statistics) {
            this.stats = { ...this.stats, ...data.statistics };
            this.saveStatistics();
            this.renderStatistics();
            return true;
        }
        return false;
    }

    resetStatistics() {
        this.stats = {
            gamesPlayed: 0,
            gamesWon: 0,
            totalScore: 0,
            bestScore: 0,
            totalMoves: 0,
            totalTime: 0,
            averageScore: 0,
            winRate: 0,
            highestTile: 0,
            streakCurrent: 0,
            streakBest: 0,
            averageTime: 0,
            averageMoves: 0,
            fastestWin: 0,
            slowestWin: 0,
            mostMoves: 0,
            leastMoves: 0,
            sessionsToday: 0,
            lastPlayDate: null,
            totalDays: 0,
            favoriteSize: 4,
            favoriteMode: 'classic'
        };
        
        this.saveStatistics();
        this.renderStatistics();
    }

    getStatistic(key) {
        return this.stats[key];
    }

    setStatistic(key, value) {
        this.stats[key] = value;
        this.saveStatistics();
    }

    incrementStatistic(key, amount = 1) {
        this.stats[key] = (this.stats[key] || 0) + amount;
        this.saveStatistics();
    }

    // 获取排行榜数据
    getLeaderboardData() {
        return {
            bestScore: this.stats.bestScore,
            highestTile: this.stats.highestTile,
            bestStreak: this.stats.streakBest,
            fastestWin: this.stats.fastestWin,
            leastMoves: this.stats.leastMoves,
            totalScore: this.stats.totalScore,
            gamesPlayed: this.stats.gamesPlayed,
            winRate: this.stats.winRate
        };
    }

    // 比较统计数据
    compareWith(otherStats) {
        const comparison = {};
        const keys = ['bestScore', 'highestTile', 'winRate', 'averageScore', 'streakBest'];
        
        keys.forEach(key => {
            const myValue = this.stats[key] || 0;
            const otherValue = otherStats[key] || 0;
            
            comparison[key] = {
                mine: myValue,
                other: otherValue,
                better: myValue > otherValue,
                difference: myValue - otherValue
            };
        });
        
        return comparison;
    }

    // 获取成就相关统计
    getAchievementStats() {
        return {
            gamesPlayed: this.stats.gamesPlayed,
            gamesWon: this.stats.gamesWon,
            bestScore: this.stats.bestScore,
            highestTile: this.stats.highestTile,
            streakCurrent: this.stats.streakCurrent,
            streakBest: this.stats.streakBest,
            totalScore: this.stats.totalScore,
            fastestWin: this.stats.fastestWin,
            leastMoves: this.stats.leastMoves
        };
    }
}

// 创建全局统计管理器实例
window.statisticsManager = new StatisticsManager();
