// 主题管理文件
class ThemeManager {
    constructor() {
        this.currentTheme = 'classic';
        this.themes = [
            'classic',
            'dark',
            'rainbow',
            'neon',
            'nature',
            'ocean'
        ];
        this.init();
    }

    init() {
        this.loadTheme();
        this.bindEvents();
    }

    loadTheme() {
        const settings = storage.get('settings', {});
        this.currentTheme = settings.theme || 'classic';
        this.applyTheme(this.currentTheme);
    }

    bindEvents() {
        // 主题选择器事件
        const themeSelector = document.getElementById('themeSelector');
        if (themeSelector) {
            themeSelector.addEventListener('click', (e) => {
                const themeItem = e.target.closest('.theme-item');
                if (themeItem) {
                    const theme = themeItem.dataset.theme;
                    this.setTheme(theme);
                    this.hideThemeSelector();
                }
            });
        }

        // 点击外部关闭主题选择器
        document.addEventListener('click', (e) => {
            const themeSelector = document.getElementById('themeSelector');
            const themeBtn = document.getElementById('themeBtn');
            
            if (themeSelector && !themeSelector.classList.contains('hidden') && 
                !themeSelector.contains(e.target) && 
                !themeBtn.contains(e.target)) {
                this.hideThemeSelector();
            }
        });
    }

    setTheme(theme) {
        if (!this.themes.includes(theme)) return;

        this.currentTheme = theme;
        this.applyTheme(theme);
        
        // 保存设置
        const settings = storage.get('settings', {});
        settings.theme = theme;
        storage.set('settings', settings);
        
        // 播放音效
        audioManager.play('button');
        
        // 创建主题特定的粒子效果
        if (particleSystem.isEnabled()) {
            particleSystem.themeParticles(theme);
        }
    }

    applyTheme(theme) {
        // 移除所有主题类
        document.body.className = '';
        
        // 添加新主题类
        document.body.classList.add(`theme-${theme}`);
        
        // 更新主题选择器中的活动项
        const themeItems = document.querySelectorAll('.theme-item');
        themeItems.forEach(item => {
            if (item.dataset.theme === theme) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
        
        // 应用主题特定的样式调整
        this.applyThemeSpecificStyles(theme);
    }

    applyThemeSpecificStyles(theme) {
        // 根据主题调整特定样式
        switch (theme) {
            case 'neon':
                this.applyNeonStyles();
                break;
            case 'dark':
                this.applyDarkStyles();
                break;
            case 'rainbow':
                this.applyRainbowStyles();
                break;
            case 'nature':
                this.applyNatureStyles();
                break;
            case 'ocean':
                this.applyOceanStyles();
                break;
            default:
                this.applyClassicStyles();
                break;
        }
    }

    applyNeonStyles() {
        // 添加霓虹发光效果
        const title = document.querySelector('.title');
        if (title) {
            title.classList.add('animate-neon');
        }
        
        // 添加按钮发光效果
        const buttons = document.querySelectorAll('.btn-primary');
        buttons.forEach(button => {
            button.classList.add('hover-glow');
        });
    }

    applyDarkStyles() {
        // 移除可能的动画效果
        const title = document.querySelector('.title');
        if (title) {
            title.classList.remove('animate-neon', 'animate-rainbow');
        }
        
        const buttons = document.querySelectorAll('.btn-primary');
        buttons.forEach(button => {
            button.classList.remove('hover-glow');
        });
    }

    applyRainbowStyles() {
        // 添加彩虹文字效果
        const title = document.querySelector('.title');
        if (title) {
            title.classList.add('animate-rainbow');
        }
    }

    applyNatureStyles() {
        // 移除可能的动画效果
        const title = document.querySelector('.title');
        if (title) {
            title.classList.remove('animate-neon', 'animate-rainbow');
        }
    }

    applyOceanStyles() {
        // 移除可能的动画效果
        const title = document.querySelector('.title');
        if (title) {
            title.classList.remove('animate-neon', 'animate-rainbow');
        }
    }

    applyClassicStyles() {
        // 移除所有特殊效果
        const title = document.querySelector('.title');
        if (title) {
            title.classList.remove('animate-neon', 'animate-rainbow');
        }
        
        const buttons = document.querySelectorAll('.btn-primary');
        buttons.forEach(button => {
            button.classList.remove('hover-glow');
        });
    }

    hideThemeSelector() {
        const themeSelector = document.getElementById('themeSelector');
        if (themeSelector) {
            themeSelector.classList.add('hidden');
        }
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    getAvailableThemes() {
        return this.themes;
    }

    // 创建自定义主题
    createCustomTheme(name, colors) {
        if (this.themes.includes(name)) {
            console.warn(`主题 "${name}" 已存在`);
            return false;
        }

        // 创建自定义CSS
        const css = this.generateThemeCSS(name, colors);
        
        // 添加到文档
        const style = document.createElement('style');
        style.id = `theme-${name}`;
        style.textContent = css;
        document.head.appendChild(style);
        
        // 添加到主题列表
        this.themes.push(name);
        
        // 添加到主题选择器
        this.addThemeToSelector(name, colors.primary || '#776e65');
        
        return true;
    }

    generateThemeCSS(name, colors) {
        return `
            .theme-${name} {
                --primary-color: ${colors.primary || '#776e65'};
                --secondary-color: ${colors.secondary || '#f9f6f2'};
                --background-color: ${colors.background || '#faf8ef'};
                --text-color: ${colors.text || '#776e65'};
                --border-color: ${colors.border || '#bbada0'};
                --tile-background: ${colors.tileBackground || '#cdc1b4'};
                --tile-2: ${colors.tile2 || '#eee4da'};
                --tile-4: ${colors.tile4 || '#ede0c8'};
                --tile-8: ${colors.tile8 || '#f2b179'};
                --tile-16: ${colors.tile16 || '#f59563'};
                --tile-32: ${colors.tile32 || '#f67c5f'};
                --tile-64: ${colors.tile64 || '#f65e3b'};
                --tile-128: ${colors.tile128 || '#edcf72'};
                --tile-256: ${colors.tile256 || '#edcc61'};
                --tile-512: ${colors.tile512 || '#edc850'};
                --tile-1024: ${colors.tile1024 || '#edc53f'};
                --tile-2048: ${colors.tile2048 || '#edc22e'};
                --tile-super: ${colors.tileSuper || '#3c3a32'};
            }
        `;
    }

    addThemeToSelector(name, color) {
        const themeSelector = document.getElementById('themeSelector');
        if (!themeSelector) return;
        
        const themeGrid = themeSelector.querySelector('.theme-grid');
        if (!themeGrid) return;
        
        const themeItem = document.createElement('div');
        themeItem.className = 'theme-item';
        themeItem.dataset.theme = name;
        themeItem.textContent = name.charAt(0).toUpperCase() + name.slice(1);
        themeItem.style.background = color;
        themeItem.style.color = this.getContrastColor(color);
        
        themeGrid.appendChild(themeItem);
    }

    getContrastColor(hexColor) {
        // 将十六进制颜色转换为RGB
        const r = parseInt(hexColor.substr(1, 2), 16);
        const g = parseInt(hexColor.substr(3, 2), 16);
        const b = parseInt(hexColor.substr(5, 2), 16);
        
        // 计算亮度
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        
        // 根据亮度返回黑色或白色
        return brightness > 128 ? '#000000' : '#ffffff';
    }

    // 删除自定义主题
    removeCustomTheme(name) {
        if (!this.themes.includes(name) || name === 'classic') {
            console.warn(`无法删除主题 "${name}"`);
            return false;
        }
        
        // 如果当前正在使用该主题，切换到经典主题
        if (this.currentTheme === name) {
            this.setTheme('classic');
        }
        
        // 从主题列表中移除
        this.themes = this.themes.filter(theme => theme !== name);
        
        // 移除CSS
        const style = document.getElementById(`theme-${name}`);
        if (style) {
            style.parentNode.removeChild(style);
        }
        
        // 从选择器中移除
        const themeItem = document.querySelector(`.theme-item[data-theme="${name}"]`);
        if (themeItem) {
            themeItem.parentNode.removeChild(themeItem);
        }
        
        return true;
    }

    // 导出主题
    exportTheme(name) {
        if (!this.themes.includes(name)) {
            console.warn(`主题 "${name}" 不存在`);
            return null;
        }
        
        // 获取主题CSS变量
        const themeStyle = window.getComputedStyle(document.body);
        const themeVars = {};
        
        [
            'primary-color', 'secondary-color', 'background-color', 'text-color',
            'border-color', 'tile-background', 'tile-2', 'tile-4', 'tile-8',
            'tile-16', 'tile-32', 'tile-64', 'tile-128', 'tile-256', 'tile-512',
            'tile-1024', 'tile-2048', 'tile-super'
        ].forEach(varName => {
            themeVars[varName] = themeStyle.getPropertyValue(`--${varName}`).trim();
        });
        
        return {
            name: name,
            variables: themeVars,
            exportDate: new Date().toISOString()
        };
    }

    // 导入主题
    importTheme(themeData) {
        if (!themeData || !themeData.name || !themeData.variables) {
            console.warn('无效的主题数据');
            return false;
        }
        
        const colors = {};
        Object.keys(themeData.variables).forEach(key => {
            const value = themeData.variables[key];
            // 将短横线命名转换为驼峰命名
            const camelKey = key.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
            colors[camelKey] = value;
        });
        
        return this.createCustomTheme(themeData.name, colors);
    }

    // 随机生成主题
    generateRandomTheme() {
        const getRandomColor = () => {
            const letters = '0123456789ABCDEF';
            let color = '#';
            for (let i = 0; i < 6; i++) {
                color += letters[Math.floor(Math.random() * 16)];
            }
            return color;
        };
        
        const name = 'random_' + Math.floor(Math.random() * 1000);
        
        const colors = {
            primary: getRandomColor(),
            secondary: getRandomColor(),
            background: getRandomColor(),
            text: getRandomColor(),
            border: getRandomColor(),
            tileBackground: getRandomColor(),
            tile2: getRandomColor(),
            tile4: getRandomColor(),
            tile8: getRandomColor(),
            tile16: getRandomColor(),
            tile32: getRandomColor(),
            tile64: getRandomColor(),
            tile128: getRandomColor(),
            tile256: getRandomColor(),
            tile512: getRandomColor(),
            tile1024: getRandomColor(),
            tile2048: getRandomColor(),
            tileSuper: getRandomColor()
        };
        
        this.createCustomTheme(name, colors);
        return name;
    }
}

// 创建全局主题管理器实例
window.themeManager = new ThemeManager();
