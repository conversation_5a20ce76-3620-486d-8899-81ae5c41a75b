// 存储管理文件
class StorageManager {
    constructor() {
        this.prefix = 'super2048_';
        this.version = '1.0.0';
        this.init();
    }

    init() {
        // 检查存储支持
        this.isSupported = this.checkSupport();
        
        // 初始化默认数据
        this.initDefaults();
        
        // 版本迁移
        this.migrate();
    }

    checkSupport() {
        try {
            const test = 'test';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            console.warn('LocalStorage 不支持，使用内存存储');
            this.memoryStorage = {};
            return false;
        }
    }

    initDefaults() {
        const defaults = {
            // 游戏设置
            settings: {
                sound: true,
                animation: true,
                particles: true,
                autoSave: true,
                theme: 'classic',
                language: 'zh-CN'
            },
            
            // 游戏统计
            statistics: {
                gamesPlayed: 0,
                gamesWon: 0,
                totalScore: 0,
                bestScore: 0,
                totalMoves: 0,
                totalTime: 0,
                averageScore: 0,
                winRate: 0,
                highestTile: 0,
                streakCurrent: 0,
                streakBest: 0
            },
            
            // 成就系统
            achievements: {},
            
            // 游戏历史
            gameHistory: [],
            
            // 当前游戏状态
            currentGame: null,
            
            // 撤销历史
            undoHistory: [],
            
            // 用户偏好
            preferences: {
                boardSize: 4,
                gameMode: 'classic',
                targetTile: 2048,
                showHints: true,
                showTutorial: true
            }
        };

        // 初始化不存在的数据
        Object.keys(defaults).forEach(key => {
            if (!this.has(key)) {
                this.set(key, defaults[key]);
            }
        });
    }

    migrate() {
        const currentVersion = this.get('version', '0.0.0');
        
        if (currentVersion !== this.version) {
            console.log(`数据迁移: ${currentVersion} -> ${this.version}`);
            
            // 执行迁移逻辑
            this.performMigration(currentVersion, this.version);
            
            // 更新版本号
            this.set('version', this.version);
        }
    }

    performMigration(fromVersion, toVersion) {
        // 这里可以添加版本迁移逻辑
        // 例如：数据结构变更、新字段添加等
        
        if (fromVersion === '0.0.0') {
            // 首次安装，无需迁移
            return;
        }
        
        // 示例迁移逻辑
        if (this.compareVersions(fromVersion, '1.0.0') < 0) {
            // 迁移到 1.0.0
            const stats = this.get('statistics', {});
            if (!stats.hasOwnProperty('streakCurrent')) {
                stats.streakCurrent = 0;
                stats.streakBest = 0;
                this.set('statistics', stats);
            }
        }
    }

    compareVersions(v1, v2) {
        const parts1 = v1.split('.').map(Number);
        const parts2 = v2.split('.').map(Number);
        
        for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
            const part1 = parts1[i] || 0;
            const part2 = parts2[i] || 0;
            
            if (part1 < part2) return -1;
            if (part1 > part2) return 1;
        }
        
        return 0;
    }

    // 基础存储操作
    set(key, value) {
        const fullKey = this.prefix + key;
        const data = JSON.stringify(value);
        
        if (this.isSupported) {
            try {
                localStorage.setItem(fullKey, data);
                return true;
            } catch (e) {
                console.error('存储失败:', e);
                return false;
            }
        } else {
            this.memoryStorage[fullKey] = data;
            return true;
        }
    }

    get(key, defaultValue = null) {
        const fullKey = this.prefix + key;
        
        try {
            let data;
            if (this.isSupported) {
                data = localStorage.getItem(fullKey);
            } else {
                data = this.memoryStorage[fullKey];
            }
            
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.error('读取失败:', e);
            return defaultValue;
        }
    }

    has(key) {
        const fullKey = this.prefix + key;
        
        if (this.isSupported) {
            return localStorage.getItem(fullKey) !== null;
        } else {
            return this.memoryStorage.hasOwnProperty(fullKey);
        }
    }

    remove(key) {
        const fullKey = this.prefix + key;
        
        if (this.isSupported) {
            localStorage.removeItem(fullKey);
        } else {
            delete this.memoryStorage[fullKey];
        }
    }

    clear() {
        if (this.isSupported) {
            const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
            keys.forEach(key => localStorage.removeItem(key));
        } else {
            this.memoryStorage = {};
        }
    }

    // 高级操作
    updateStatistics(updates) {
        const stats = this.get('statistics', {});
        Object.assign(stats, updates);
        
        // 计算衍生统计
        if (stats.gamesPlayed > 0) {
            stats.averageScore = Math.round(stats.totalScore / stats.gamesPlayed);
            stats.winRate = Math.round((stats.gamesWon / stats.gamesPlayed) * 100);
        }
        
        this.set('statistics', stats);
        return stats;
    }

    saveGameState(gameState) {
        this.set('currentGame', gameState);
        
        // 自动保存到历史
        if (this.get('settings').autoSave) {
            this.addToHistory(gameState);
        }
    }

    loadGameState() {
        return this.get('currentGame');
    }

    addToHistory(gameState) {
        const history = this.get('gameHistory', []);
        const entry = {
            ...gameState,
            timestamp: Date.now(),
            id: Utils.generateUUID()
        };
        
        history.unshift(entry);
        
        // 限制历史记录数量
        if (history.length > 100) {
            history.splice(100);
        }
        
        this.set('gameHistory', history);
    }

    getHistory(limit = 10) {
        const history = this.get('gameHistory', []);
        return history.slice(0, limit);
    }

    clearHistory() {
        this.set('gameHistory', []);
    }

    // 成就系统
    unlockAchievement(achievementId) {
        const achievements = this.get('achievements', {});
        if (!achievements[achievementId]) {
            achievements[achievementId] = {
                unlocked: true,
                timestamp: Date.now()
            };
            this.set('achievements', achievements);
            return true;
        }
        return false;
    }

    isAchievementUnlocked(achievementId) {
        const achievements = this.get('achievements', {});
        return achievements[achievementId]?.unlocked || false;
    }

    getUnlockedAchievements() {
        const achievements = this.get('achievements', {});
        return Object.keys(achievements).filter(id => achievements[id].unlocked);
    }

    // 撤销系统
    saveUndoState(state) {
        const undoHistory = this.get('undoHistory', []);
        undoHistory.push({
            state: Utils.deepClone(state),
            timestamp: Date.now()
        });
        
        // 限制撤销历史数量
        if (undoHistory.length > 10) {
            undoHistory.shift();
        }
        
        this.set('undoHistory', undoHistory);
    }

    getLastUndoState() {
        const undoHistory = this.get('undoHistory', []);
        return undoHistory.length > 0 ? undoHistory.pop().state : null;
    }

    clearUndoHistory() {
        this.set('undoHistory', []);
    }

    // 数据导出/导入
    exportData() {
        const data = {};
        const keys = this.getAllKeys();
        
        keys.forEach(key => {
            data[key] = this.get(key);
        });
        
        return {
            version: this.version,
            timestamp: Date.now(),
            data: data
        };
    }

    importData(importData) {
        try {
            if (!importData.data || !importData.version) {
                throw new Error('无效的数据格式');
            }
            
            // 备份当前数据
            const backup = this.exportData();
            
            // 导入新数据
            Object.keys(importData.data).forEach(key => {
                this.set(key, importData.data[key]);
            });
            
            // 执行迁移（如果需要）
            if (importData.version !== this.version) {
                this.performMigration(importData.version, this.version);
            }
            
            return { success: true, backup };
        } catch (error) {
            console.error('数据导入失败:', error);
            return { success: false, error: error.message };
        }
    }

    getAllKeys() {
        if (this.isSupported) {
            return Object.keys(localStorage)
                .filter(key => key.startsWith(this.prefix))
                .map(key => key.substring(this.prefix.length));
        } else {
            return Object.keys(this.memoryStorage)
                .filter(key => key.startsWith(this.prefix))
                .map(key => key.substring(this.prefix.length));
        }
    }

    // 获取存储使用情况
    getStorageInfo() {
        if (!this.isSupported) {
            return {
                used: Object.keys(this.memoryStorage).length,
                total: 'unlimited',
                percentage: 0
            };
        }
        
        try {
            let used = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    used += localStorage[key].length + key.length;
                }
            }
            
            // 估算总容量（通常为 5-10MB）
            const total = 5 * 1024 * 1024; // 5MB
            const percentage = (used / total) * 100;
            
            return {
                used: used,
                total: total,
                percentage: Math.round(percentage)
            };
        } catch (e) {
            return {
                used: 0,
                total: 0,
                percentage: 0
            };
        }
    }
}

// 创建全局存储管理器实例
window.storage = new StorageManager();
