<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级2048 - 增强版</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link rel="stylesheet" href="styles/animations.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 主容器 -->
    <div id="app" class="app">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">超级2048</h1>
                <div class="header-controls">
                    <button id="menuBtn" class="btn btn-icon">☰</button>
                    <button id="fullscreenBtn" class="btn btn-icon">⛶</button>
                    <button id="themeBtn" class="btn btn-icon">🎨</button>
                </div>
            </div>
        </header>

        <!-- 游戏信息面板 -->
        <div class="game-info">
            <div class="score-container">
                <div class="score-box">
                    <div class="score-label">分数</div>
                    <div id="score" class="score-value">0</div>
                </div>
                <div class="score-box">
                    <div class="score-label">最高分</div>
                    <div id="bestScore" class="score-value">0</div>
                </div>
                <div class="score-box">
                    <div class="score-label">移动次数</div>
                    <div id="moves" class="score-value">0</div>
                </div>
                <div class="score-box">
                    <div class="score-label">时间</div>
                    <div id="timer" class="score-value">00:00</div>
                </div>
            </div>
        </div>

        <!-- 游戏控制面板 -->
        <div class="game-controls">
            <div class="control-group">
                <button id="newGameBtn" class="btn btn-primary">新游戏</button>
                <button id="undoBtn" class="btn btn-secondary">撤销</button>
                <button id="hintBtn" class="btn btn-secondary">提示</button>
                <button id="pauseBtn" class="btn btn-secondary">暂停</button>
            </div>
            <div class="control-group">
                <select id="gameMode" class="select">
                    <option value="4">经典 4x4</option>
                    <option value="3">简单 3x3</option>
                    <option value="5">困难 5x5</option>
                    <option value="6">专家 6x6</option>
                </select>
                <select id="gameType" class="select">
                    <option value="classic">经典模式</option>
                    <option value="time">时间挑战</option>
                    <option value="endless">无尽模式</option>
                    <option value="target">目标模式</option>
                </select>
            </div>
        </div>

        <!-- 游戏板 -->
        <div class="game-container">
            <div id="gameBoard" class="game-board">
                <!-- 游戏网格将在这里动态生成 -->
            </div>
            <div id="gameOverlay" class="game-overlay hidden">
                <div class="overlay-content">
                    <h2 id="overlayTitle">游戏结束</h2>
                    <p id="overlayMessage">再试一次？</p>
                    <div class="overlay-buttons">
                        <button id="restartBtn" class="btn btn-primary">重新开始</button>
                        <button id="shareBtn" class="btn btn-secondary">分享</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏菜单 -->
        <div id="sidebar" class="sidebar hidden">
            <div class="sidebar-content">
                <h3>游戏菜单</h3>
                <div class="menu-section">
                    <h4>统计数据</h4>
                    <div id="statistics" class="statistics">
                        <!-- 统计数据将在这里显示 -->
                    </div>
                </div>
                <div class="menu-section">
                    <h4>成就</h4>
                    <div id="achievements" class="achievements">
                        <!-- 成就将在这里显示 -->
                    </div>
                </div>
                <div class="menu-section">
                    <h4>设置</h4>
                    <div class="settings">
                        <label class="setting-item">
                            <span>音效</span>
                            <input type="checkbox" id="soundToggle" checked>
                        </label>
                        <label class="setting-item">
                            <span>动画</span>
                            <input type="checkbox" id="animationToggle" checked>
                        </label>
                        <label class="setting-item">
                            <span>粒子效果</span>
                            <input type="checkbox" id="particleToggle" checked>
                        </label>
                        <label class="setting-item">
                            <span>自动保存</span>
                            <input type="checkbox" id="autoSaveToggle" checked>
                        </label>
                    </div>
                </div>
                <div class="menu-section">
                    <h4>数据管理</h4>
                    <div class="data-controls">
                        <button id="exportBtn" class="btn btn-small">导出数据</button>
                        <button id="importBtn" class="btn btn-small">导入数据</button>
                        <button id="resetBtn" class="btn btn-small btn-danger">重置数据</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主题选择器 -->
        <div id="themeSelector" class="theme-selector hidden">
            <div class="theme-grid">
                <div class="theme-item" data-theme="classic">经典</div>
                <div class="theme-item" data-theme="dark">暗黑</div>
                <div class="theme-item" data-theme="rainbow">彩虹</div>
                <div class="theme-item" data-theme="neon">霓虹</div>
                <div class="theme-item" data-theme="nature">自然</div>
                <div class="theme-item" data-theme="ocean">海洋</div>
            </div>
        </div>

        <!-- 教程模式 -->
        <div id="tutorial" class="tutorial hidden">
            <div class="tutorial-content">
                <h3>游戏教程</h3>
                <div id="tutorialSteps" class="tutorial-steps">
                    <!-- 教程步骤将在这里显示 -->
                </div>
                <div class="tutorial-controls">
                    <button id="prevTutorialBtn" class="btn btn-secondary">上一步</button>
                    <button id="nextTutorialBtn" class="btn btn-primary">下一步</button>
                    <button id="skipTutorialBtn" class="btn btn-secondary">跳过</button>
                </div>
            </div>
        </div>

        <!-- 粒子系统容器 -->
        <div id="particles" class="particles"></div>

        <!-- 音频元素 -->
        <audio id="moveSound" preload="auto">
            <source src="sounds/move.mp3" type="audio/mpeg">
        </audio>
        <audio id="mergeSound" preload="auto">
            <source src="sounds/merge.mp3" type="audio/mpeg">
        </audio>
        <audio id="winSound" preload="auto">
            <source src="sounds/win.mp3" type="audio/mpeg">
        </audio>
        <audio id="gameOverSound" preload="auto">
            <source src="sounds/gameover.mp3" type="audio/mpeg">
        </audio>
    </div>

    <!-- 脚本文件 -->
    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/themes.js"></script>
    <script src="js/achievements.js"></script>
    <script src="js/statistics.js"></script>
    <script src="js/tutorial.js"></script>
    <script src="js/game.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html>