// 主入口文件
class App {
    constructor() {
        this.game = null;
        this.ui = null;
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 初始化各个系统
            await this.initializeSystems();
            
            // 创建游戏实例
            this.createGame();
            
            // 初始化UI
            this.initializeUI();
            
            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 加载保存的游戏状态
            this.loadGameState();
            
            // 标记为已初始化
            this.isInitialized = true;
            
            console.log('超级2048 初始化完成！');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showErrorMessage('游戏初始化失败，请刷新页面重试。');
        }
    }

    async initializeSystems() {
        // 检查浏览器支持
        const support = Utils.checkSupport();
        if (!support.localStorage) {
            console.warn('LocalStorage 不支持，将使用内存存储');
        }

        // 初始化音频系统
        if (audioManager) {
            audioManager.preload();
        }

        // 初始化主题系统
        if (themeManager) {
            themeManager.loadTheme();
        }

        // 初始化统计系统
        if (statisticsManager) {
            statisticsManager.renderStatistics();
        }

        // 初始化成就系统
        if (achievementSystem) {
            achievementSystem.renderAchievementsList();
        }

        // 解锁音频上下文（移动设备需要用户交互）
        this.unlockAudioContext();
    }

    createGame() {
        this.game = new Game2048();
        
        // 设置游戏事件监听
        this.bindGameEvents();
    }

    initializeUI() {
        this.ui = new UIManager();
        this.ui.setGame(this.game);
        
        // 绑定UI事件
        this.bindUIEvents();
    }

    bindGameEvents() {
        // 这里可以添加游戏事件监听
        // 例如：游戏结束、分数更新等
    }

    bindUIEvents() {
        // 设置切换事件
        this.bindSettingsEvents();
        
        // 数据管理事件
        this.bindDataEvents();
        
        // 主题切换事件
        this.bindThemeEvents();
    }

    bindSettingsEvents() {
        // 音效开关
        const soundToggle = document.getElementById('soundToggle');
        if (soundToggle) {
            soundToggle.addEventListener('change', (e) => {
                audioManager.setEnabled(e.target.checked);
            });
        }

        // 动画开关
        const animationToggle = document.getElementById('animationToggle');
        if (animationToggle) {
            animationToggle.addEventListener('change', (e) => {
                animationManager.setEnabled(e.target.checked);
            });
        }

        // 粒子效果开关
        const particleToggle = document.getElementById('particleToggle');
        if (particleToggle) {
            particleToggle.addEventListener('change', (e) => {
                particleSystem.setEnabled(e.target.checked);
            });
        }

        // 自动保存开关
        const autoSaveToggle = document.getElementById('autoSaveToggle');
        if (autoSaveToggle) {
            autoSaveToggle.addEventListener('change', (e) => {
                const settings = storage.get('settings', {});
                settings.autoSave = e.target.checked;
                storage.set('settings', settings);
            });
        }
    }

    bindDataEvents() {
        // 导出数据
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportGameData();
            });
        }

        // 导入数据
        const importBtn = document.getElementById('importBtn');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                this.importGameData();
            });
        }

        // 重置数据
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetGameData();
            });
        }
    }

    bindThemeEvents() {
        // 主题选择事件已在ThemeManager中处理
        const themeItems = document.querySelectorAll('.theme-item');
        themeItems.forEach(item => {
            item.addEventListener('click', () => {
                audioManager.play('button');
            });
        });
    }

    bindGlobalEvents() {
        // 窗口失焦时暂停游戏
        window.addEventListener('blur', () => {
            if (this.game && !this.game.isGameOver) {
                this.game.pause();
            }
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.game && !this.game.isGameOver) {
                this.game.pause();
            }
        });

        // 错误处理
        window.addEventListener('error', (e) => {
            Utils.handleError(e.error, '全局错误');
        });

        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            Utils.handleError(e.reason, 'Promise拒绝');
        });

        // 在线状态变化
        window.addEventListener('online', () => {
            this.ui.showToast('网络连接已恢复');
        });

        window.addEventListener('offline', () => {
            this.ui.showToast('网络连接已断开');
        });
    }

    loadGameState() {
        // 尝试加载保存的游戏状态
        if (this.game.loadGameState()) {
            this.ui.updateBoard();
            console.log('已加载保存的游戏状态');
        }
    }

    unlockAudioContext() {
        // 移动设备需要用户交互来解锁音频上下文
        const unlockAudio = () => {
            if (audioManager) {
                audioManager.unlock();
            }
            
            // 移除事件监听器
            document.removeEventListener('touchstart', unlockAudio);
            document.removeEventListener('click', unlockAudio);
        };

        document.addEventListener('touchstart', unlockAudio);
        document.addEventListener('click', unlockAudio);
    }

    exportGameData() {
        try {
            const data = {
                version: '1.0.0',
                timestamp: Date.now(),
                storage: storage.exportData(),
                statistics: statisticsManager.exportStatistics(),
                achievements: achievementSystem.exportAchievements(),
                settings: storage.get('settings', {}),
                preferences: storage.get('preferences', {})
            };

            const jsonData = JSON.stringify(data, null, 2);
            const filename = `super2048_backup_${new Date().toISOString().split('T')[0]}.json`;
            
            Utils.downloadFile(jsonData, filename, 'application/json');
            this.ui.showToast('数据导出成功！');
            
        } catch (error) {
            console.error('数据导出失败:', error);
            this.ui.showToast('数据导出失败');
        }
    }

    async importGameData() {
        try {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                
                try {
                    const content = await Utils.readFile(file);
                    const data = JSON.parse(content);
                    
                    if (!data.version || !data.storage) {
                        throw new Error('无效的备份文件格式');
                    }
                    
                    // 确认导入
                    if (!confirm('导入数据将覆盖当前所有数据，确定要继续吗？')) {
                        return;
                    }
                    
                    // 导入数据
                    const result = storage.importData(data.storage);
                    if (result.success) {
                        // 重新加载各个系统
                        if (data.statistics) {
                            statisticsManager.importStatistics(data.statistics);
                        }
                        
                        if (data.achievements) {
                            achievementSystem.importAchievements(data.achievements);
                        }
                        
                        // 重新加载游戏
                        this.game.loadGameState();
                        this.ui.updateBoard();
                        
                        this.ui.showToast('数据导入成功！');
                    } else {
                        throw new Error(result.error);
                    }
                    
                } catch (error) {
                    console.error('数据导入失败:', error);
                    this.ui.showToast('数据导入失败: ' + error.message);
                }
            };
            
            input.click();
            
        } catch (error) {
            console.error('导入操作失败:', error);
            this.ui.showToast('导入操作失败');
        }
    }

    resetGameData() {
        if (!confirm('确定要重置所有数据吗？这个操作无法撤销！')) {
            return;
        }
        
        if (!confirm('最后确认：这将删除所有游戏记录、统计数据和成就！')) {
            return;
        }
        
        try {
            // 清除所有数据
            storage.clear();
            statisticsManager.resetStatistics();
            achievementSystem.resetAchievements();
            
            // 重新开始游戏
            this.game.restart();
            this.ui.updateBoard();
            
            this.ui.showToast('所有数据已重置');
            
        } catch (error) {
            console.error('数据重置失败:', error);
            this.ui.showToast('数据重置失败');
        }
    }

    showErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 8px;
            z-index: 9999;
            text-align: center;
            max-width: 400px;
        `;
        errorDiv.innerHTML = `
            <h3>错误</h3>
            <p>${message}</p>
            <button onclick="location.reload()" style="
                background: white;
                color: #dc3545;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">刷新页面</button>
        `;
        
        document.body.appendChild(errorDiv);
    }

    // 获取应用状态
    getAppState() {
        return {
            isInitialized: this.isInitialized,
            gameState: this.game ? {
                score: this.game.score,
                moves: this.game.moves,
                gameTime: this.game.gameTime,
                isGameOver: this.game.isGameOver,
                isWon: this.game.isWon
            } : null,
            settings: storage.get('settings', {}),
            statistics: statisticsManager.getDetailedReport()
        };
    }

    // 调试信息
    getDebugInfo() {
        return {
            appState: this.getAppState(),
            browserSupport: Utils.checkSupport(),
            deviceInfo: Utils.getDeviceInfo(),
            storageInfo: storage.getStorageInfo(),
            performance: {
                memory: performance.memory ? {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB'
                } : 'N/A'
            }
        };
    }
}

// 创建应用实例
window.app = new App();

// 开发者工具
if (typeof window !== 'undefined') {
    window.debug = {
        app: () => window.app,
        game: () => window.app.game,
        storage: () => window.storage,
        stats: () => window.statisticsManager,
        achievements: () => window.achievementSystem,
        audio: () => window.audioManager,
        particles: () => window.particleSystem,
        themes: () => window.themeManager,
        animations: () => window.animationManager,
        tutorial: () => window.tutorialManager,
        info: () => window.app.getDebugInfo(),
        exportData: () => window.app.exportGameData(),
        resetAll: () => window.app.resetGameData()
    };
    
    console.log('🎮 超级2048 开发者工具已加载');
    console.log('使用 debug.info() 查看调试信息');
    console.log('使用 debug.game() 访问游戏实例');
}

// 服务工作者注册（用于离线支持）
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('Service Worker 注册成功');
            })
            .catch(error => {
                console.log('Service Worker 注册失败');
            });
    });
}
