// 教程系统文件
class TutorialManager {
    constructor() {
        this.currentStep = 0;
        this.isActive = false;
        this.steps = [];
        this.init();
    }

    init() {
        this.defineTutorialSteps();
        this.bindEvents();
        this.checkFirstTime();
    }

    defineTutorialSteps() {
        this.steps = [
            {
                title: '欢迎来到超级2048！',
                content: '这是一个增强版的2048游戏，包含了许多创新功能。让我们开始学习如何游戏吧！',
                target: null,
                action: null
            },
            {
                title: '游戏目标',
                content: '通过滑动瓦片来合并相同数字，目标是创造出2048瓦片。当两个相同数字的瓦片碰撞时，它们会合并成一个更大的数字。',
                target: '#gameBoard',
                action: 'highlight'
            },
            {
                title: '控制方式',
                content: '你可以使用方向键、WASD键或者触摸滑动来移动瓦片。所有瓦片会向你选择的方向移动。',
                target: '#gameBoard',
                action: 'showControls'
            },
            {
                title: '分数系统',
                content: '每次合并瓦片时，你会获得等于新瓦片数值的分数。尽量获得更高的分数吧！',
                target: '#score',
                action: 'highlight'
            },
            {
                title: '撤销功能',
                content: '如果你犯了错误，可以点击撤销按钮回到上一步。但要明智地使用这个功能！',
                target: '#undoBtn',
                action: 'highlight'
            },
            {
                title: '提示系统',
                content: '当你不知道下一步该怎么走时，可以点击提示按钮获得建议。',
                target: '#hintBtn',
                action: 'highlight'
            },
            {
                title: '游戏模式',
                content: '你可以选择不同的游戏模式：经典模式、时间挑战、无尽模式等。',
                target: '#gameType',
                action: 'highlight'
            },
            {
                title: '棋盘大小',
                content: '除了经典的4x4棋盘，你还可以尝试3x3、5x5或6x6的棋盘，体验不同的挑战。',
                target: '#gameMode',
                action: 'highlight'
            },
            {
                title: '主题系统',
                content: '游戏提供了多种主题供你选择，包括经典、暗黑、彩虹、霓虹等风格。',
                target: '#themeBtn',
                action: 'highlight'
            },
            {
                title: '成就系统',
                content: '完成特定目标可以解锁成就，查看你的游戏进度和里程碑。',
                target: '#menuBtn',
                action: 'highlight'
            },
            {
                title: '统计数据',
                content: '游戏会记录你的详细统计数据，包括游戏次数、胜率、最高分等。',
                target: '#menuBtn',
                action: 'highlight'
            },
            {
                title: '开始游戏！',
                content: '现在你已经了解了基本操作，开始你的2048之旅吧！记住，策略比速度更重要。',
                target: null,
                action: null
            }
        ];
    }

    bindEvents() {
        const nextBtn = document.getElementById('nextTutorialBtn');
        const prevBtn = document.getElementById('prevTutorialBtn');
        const skipBtn = document.getElementById('skipTutorialBtn');

        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextStep());
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.prevStep());
        }

        if (skipBtn) {
            skipBtn.addEventListener('click', () => this.skipTutorial());
        }

        // ESC键跳过教程
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isActive) {
                this.skipTutorial();
            }
        });
    }

    checkFirstTime() {
        const preferences = storage.get('preferences', {});
        if (preferences.showTutorial !== false && !storage.has('currentGame')) {
            // 延迟显示教程，让页面完全加载
            setTimeout(() => {
                this.startTutorial();
            }, 1000);
        }
    }

    startTutorial() {
        this.isActive = true;
        this.currentStep = 0;
        this.showTutorial();
        this.showStep(0);
    }

    showTutorial() {
        const tutorial = document.getElementById('tutorial');
        if (tutorial) {
            tutorial.classList.remove('hidden');
        }
    }

    hideTutorial() {
        const tutorial = document.getElementById('tutorial');
        if (tutorial) {
            tutorial.classList.add('hidden');
        }
        this.clearHighlights();
        this.isActive = false;
    }

    showStep(stepIndex) {
        if (stepIndex < 0 || stepIndex >= this.steps.length) return;

        this.currentStep = stepIndex;
        const step = this.steps[stepIndex];

        // 更新教程内容
        const stepsContainer = document.getElementById('tutorialSteps');
        if (stepsContainer) {
            stepsContainer.innerHTML = `
                <div class="tutorial-step active">
                    <h4>${step.title}</h4>
                    <p>${step.content}</p>
                    <div class="tutorial-progress">
                        <span>步骤 ${stepIndex + 1} / ${this.steps.length}</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${((stepIndex + 1) / this.steps.length) * 100}%"></div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 更新按钮状态
        this.updateButtons();

        // 执行步骤动作
        this.executeStepAction(step);
    }

    executeStepAction(step) {
        this.clearHighlights();

        if (!step.target || !step.action) return;

        const target = document.querySelector(step.target);
        if (!target) return;

        switch (step.action) {
            case 'highlight':
                this.highlightElement(target);
                break;
            case 'showControls':
                this.showControlsOverlay();
                break;
        }
    }

    highlightElement(element) {
        // 添加高亮效果
        element.classList.add('tutorial-highlight');
        
        // 创建指示箭头
        const arrow = document.createElement('div');
        arrow.className = 'tutorial-arrow';
        arrow.innerHTML = '↑';
        arrow.style.cssText = `
            position: absolute;
            font-size: 24px;
            color: #f67c5f;
            z-index: 1002;
            animation: bounce 1s infinite;
        `;

        // 定位箭头
        const rect = element.getBoundingClientRect();
        arrow.style.left = (rect.left + rect.width / 2 - 12) + 'px';
        arrow.style.top = (rect.top - 40) + 'px';

        document.body.appendChild(arrow);
        arrow.classList.add('tutorial-element');

        // 滚动到元素
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    showControlsOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'controls-overlay tutorial-element';
        overlay.innerHTML = `
            <div class="controls-grid">
                <div class="control-key">↑</div>
                <div class="control-key">←</div>
                <div class="control-key">↓</div>
                <div class="control-key">→</div>
            </div>
            <p>使用方向键或WASD移动瓦片</p>
        `;
        
        overlay.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 30px;
            border-radius: 12px;
            z-index: 1002;
            text-align: center;
        `;

        document.body.appendChild(overlay);
    }

    clearHighlights() {
        // 移除高亮效果
        document.querySelectorAll('.tutorial-highlight').forEach(el => {
            el.classList.remove('tutorial-highlight');
        });

        // 移除教程元素
        document.querySelectorAll('.tutorial-element').forEach(el => {
            el.parentNode.removeChild(el);
        });
    }

    updateButtons() {
        const nextBtn = document.getElementById('nextTutorialBtn');
        const prevBtn = document.getElementById('prevTutorialBtn');

        if (prevBtn) {
            prevBtn.disabled = this.currentStep === 0;
        }

        if (nextBtn) {
            if (this.currentStep === this.steps.length - 1) {
                nextBtn.textContent = '完成';
            } else {
                nextBtn.textContent = '下一步';
            }
        }
    }

    nextStep() {
        if (this.currentStep < this.steps.length - 1) {
            this.showStep(this.currentStep + 1);
        } else {
            this.completeTutorial();
        }
    }

    prevStep() {
        if (this.currentStep > 0) {
            this.showStep(this.currentStep - 1);
        }
    }

    skipTutorial() {
        this.completeTutorial();
    }

    completeTutorial() {
        this.hideTutorial();
        
        // 保存设置，不再显示教程
        const preferences = storage.get('preferences', {});
        preferences.showTutorial = false;
        storage.set('preferences', preferences);

        // 显示完成消息
        this.showCompletionMessage();
    }

    showCompletionMessage() {
        const message = document.createElement('div');
        message.className = 'tutorial-completion';
        message.innerHTML = `
            <div class="completion-content">
                <h3>🎉 教程完成！</h3>
                <p>你已经掌握了游戏的基本操作，现在开始享受游戏吧！</p>
                <button class="btn btn-primary" onclick="this.parentNode.parentNode.remove()">开始游戏</button>
            </div>
        `;
        
        message.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        `;

        document.body.appendChild(message);

        // 自动移除
        setTimeout(() => {
            if (message.parentNode) {
                message.parentNode.removeChild(message);
            }
        }, 5000);
    }

    // 重新开始教程
    restartTutorial() {
        this.startTutorial();
    }

    // 检查是否应该显示特定提示
    showContextualTip(context) {
        const tips = {
            'first_move': {
                title: '第一步',
                content: '试着向一个方向移动瓦片，看看会发生什么！'
            },
            'first_merge': {
                title: '很好！',
                content: '你成功合并了两个瓦片！继续寻找合并机会。'
            },
            'board_full': {
                title: '小心！',
                content: '棋盘快满了，考虑使用提示功能或者撤销上一步。'
            },
            'high_score': {
                title: '新纪录！',
                content: '恭喜你创造了新的最高分记录！'
            }
        };

        const tip = tips[context];
        if (!tip) return;

        this.showQuickTip(tip.title, tip.content);
    }

    showQuickTip(title, content) {
        const tip = document.createElement('div');
        tip.className = 'quick-tip';
        tip.innerHTML = `
            <div class="tip-content">
                <h4>${title}</h4>
                <p>${content}</p>
            </div>
        `;
        
        tip.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            max-width: 250px;
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(tip);

        // 自动移除
        setTimeout(() => {
            tip.style.animation = 'slideInRight 0.3s ease-out reverse';
            setTimeout(() => {
                if (tip.parentNode) {
                    tip.parentNode.removeChild(tip);
                }
            }, 300);
        }, 3000);
    }

    // 获取帮助信息
    getHelp(topic) {
        const helpTopics = {
            'controls': {
                title: '控制方式',
                content: `
                    <ul>
                        <li>方向键：↑↓←→</li>
                        <li>WASD键：W(上) A(左) S(下) D(右)</li>
                        <li>触摸滑动：在移动设备上滑动</li>
                        <li>鼠标拖拽：在桌面设备上拖拽</li>
                    </ul>
                `
            },
            'scoring': {
                title: '计分规则',
                content: `
                    <ul>
                        <li>合并瓦片时获得分数</li>
                        <li>分数等于新瓦片的数值</li>
                        <li>连续合并可以获得更高分数</li>
                        <li>策略性移动比快速移动更重要</li>
                    </ul>
                `
            },
            'strategy': {
                title: '游戏策略',
                content: `
                    <ul>
                        <li>保持最大瓦片在角落</li>
                        <li>尽量保持一个方向的单调性</li>
                        <li>不要随意填满棋盘</li>
                        <li>计划几步之后的移动</li>
                    </ul>
                `
            }
        };

        const help = helpTopics[topic];
        if (help) {
            this.showHelpDialog(help.title, help.content);
        }
    }

    showHelpDialog(title, content) {
        const dialog = document.createElement('div');
        dialog.className = 'help-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <h3>${title}</h3>
                <div class="dialog-body">${content}</div>
                <button class="btn btn-primary" onclick="this.parentNode.parentNode.remove()">关闭</button>
            </div>
        `;
        
        dialog.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1003;
        `;

        document.body.appendChild(dialog);
    }
}

// 创建全局教程管理器实例
window.tutorialManager = new TutorialManager();
