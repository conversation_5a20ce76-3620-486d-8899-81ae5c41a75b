// 游戏核心逻辑文件
class Game2048 {
    constructor() {
        this.size = 4;
        this.board = [];
        this.score = 0;
        this.bestScore = 0;
        this.moves = 0;
        this.startTime = 0;
        this.gameTime = 0;
        this.gameMode = 'classic';
        this.targetTile = 2048;
        this.isGameOver = false;
        this.isWon = false;
        this.isPaused = false;
        this.canUndo = false;
        this.previousState = null;
        this.moveHistory = [];
        this.init();
    }

    init() {
        this.loadSettings();
        this.loadBestScore();
        this.initBoard();
        this.addRandomTile();
        this.addRandomTile();
        this.startTime = Date.now();
        this.startTimer();
    }

    loadSettings() {
        const preferences = storage.get('preferences', {});
        this.size = preferences.boardSize || 4;
        this.gameMode = preferences.gameMode || 'classic';
        this.targetTile = preferences.targetTile || 2048;
    }

    loadBestScore() {
        const stats = storage.get('statistics', {});
        this.bestScore = stats.bestScore || 0;
    }

    initBoard() {
        this.board = [];
        for (let i = 0; i < this.size; i++) {
            this.board[i] = [];
            for (let j = 0; j < this.size; j++) {
                this.board[i][j] = 0;
            }
        }
    }

    startTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
        }
        
        this.timerInterval = setInterval(() => {
            if (!this.isPaused && !this.isGameOver) {
                this.gameTime = Math.floor((Date.now() - this.startTime) / 1000);
                this.updateTimerDisplay();
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const timerElement = document.getElementById('timer');
        if (timerElement) {
            timerElement.textContent = Utils.formatTime(this.gameTime);
        }
    }

    addRandomTile() {
        const emptyCells = this.getEmptyCells();
        if (emptyCells.length === 0) return false;

        const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
        const value = Math.random() < 0.9 ? 2 : 4;
        
        this.board[randomCell.row][randomCell.col] = value;
        
        // 播放音效
        audioManager.play('newTile');
        
        return true;
    }

    getEmptyCells() {
        const emptyCells = [];
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] === 0) {
                    emptyCells.push({ row: i, col: j });
                }
            }
        }
        return emptyCells;
    }

    saveState() {
        this.previousState = {
            board: Utils.deepClone(this.board),
            score: this.score,
            moves: this.moves,
            gameTime: this.gameTime
        };
        this.canUndo = true;
        
        // 保存到撤销历史
        storage.saveUndoState(this.previousState);
    }

    move(direction) {
        if (this.isGameOver || this.isPaused) return false;

        this.saveState();
        
        let moved = false;
        let mergedScore = 0;
        const mergedPositions = [];

        switch (direction) {
            case 'left':
                moved = this.moveLeft();
                break;
            case 'right':
                moved = this.moveRight();
                break;
            case 'up':
                moved = this.moveUp();
                break;
            case 'down':
                moved = this.moveDown();
                break;
        }

        if (moved) {
            this.moves++;
            this.addRandomTile();
            this.checkGameState();
            this.updateScore();
            this.saveGameState();
            
            // 记录移动历史
            this.moveHistory.push({
                direction: direction,
                timestamp: Date.now(),
                score: this.score,
                board: Utils.deepClone(this.board)
            });
            
            // 播放音效
            audioManager.play('move');
            
            return true;
        } else {
            this.canUndo = false;
            return false;
        }
    }

    moveLeft() {
        let moved = false;
        
        for (let i = 0; i < this.size; i++) {
            const row = this.board[i];
            const newRow = this.slideArray(row);
            
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] !== newRow[j]) {
                    moved = true;
                }
                this.board[i][j] = newRow[j];
            }
        }
        
        return moved;
    }

    moveRight() {
        let moved = false;
        
        for (let i = 0; i < this.size; i++) {
            const row = this.board[i].slice().reverse();
            const newRow = this.slideArray(row).reverse();
            
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] !== newRow[j]) {
                    moved = true;
                }
                this.board[i][j] = newRow[j];
            }
        }
        
        return moved;
    }

    moveUp() {
        let moved = false;
        
        for (let j = 0; j < this.size; j++) {
            const column = [];
            for (let i = 0; i < this.size; i++) {
                column.push(this.board[i][j]);
            }
            
            const newColumn = this.slideArray(column);
            
            for (let i = 0; i < this.size; i++) {
                if (this.board[i][j] !== newColumn[i]) {
                    moved = true;
                }
                this.board[i][j] = newColumn[i];
            }
        }
        
        return moved;
    }

    moveDown() {
        let moved = false;
        
        for (let j = 0; j < this.size; j++) {
            const column = [];
            for (let i = this.size - 1; i >= 0; i--) {
                column.push(this.board[i][j]);
            }
            
            const newColumn = this.slideArray(column);
            
            for (let i = 0; i < this.size; i++) {
                if (this.board[this.size - 1 - i][j] !== newColumn[i]) {
                    moved = true;
                }
                this.board[this.size - 1 - i][j] = newColumn[i];
            }
        }
        
        return moved;
    }

    slideArray(arr) {
        const filtered = arr.filter(val => val !== 0);
        const missing = this.size - filtered.length;
        const zeros = Array(missing).fill(0);
        const newArray = filtered.concat(zeros);
        
        // 合并相同的数字
        for (let i = 0; i < this.size - 1; i++) {
            if (newArray[i] !== 0 && newArray[i] === newArray[i + 1]) {
                newArray[i] *= 2;
                newArray[i + 1] = 0;
                this.score += newArray[i];
                
                // 播放合并音效
                audioManager.play('merge');
                
                // 创建粒子效果
                if (particleSystem.isEnabled()) {
                    // 这里需要传入实际的位置坐标
                    particleSystem.explode(0, 0, { 
                        colors: this.getTileColors(newArray[i]) 
                    });
                }
            }
        }
        
        // 再次过滤和填充
        const filtered2 = newArray.filter(val => val !== 0);
        const missing2 = this.size - filtered2.length;
        const zeros2 = Array(missing2).fill(0);
        
        return filtered2.concat(zeros2);
    }

    getTileColors(value) {
        const colorMap = {
            2: ['#eee4da'],
            4: ['#ede0c8'],
            8: ['#f2b179'],
            16: ['#f59563'],
            32: ['#f67c5f'],
            64: ['#f65e3b'],
            128: ['#edcf72'],
            256: ['#edcc61'],
            512: ['#edc850'],
            1024: ['#edc53f'],
            2048: ['#edc22e']
        };
        
        return colorMap[value] || ['#3c3a32'];
    }

    checkGameState() {
        // 检查是否获胜
        if (!this.isWon && this.hasReachedTarget()) {
            this.isWon = true;
            this.handleWin();
        }
        
        // 检查是否游戏结束
        if (this.isGameOverCondition()) {
            this.isGameOver = true;
            this.handleGameOver();
        }
    }

    hasReachedTarget() {
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] >= this.targetTile) {
                    return true;
                }
            }
        }
        return false;
    }

    isGameOverCondition() {
        // 检查是否有空格
        if (this.getEmptyCells().length > 0) {
            return false;
        }
        
        // 检查是否可以合并
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                const current = this.board[i][j];
                
                // 检查右边
                if (j < this.size - 1 && current === this.board[i][j + 1]) {
                    return false;
                }
                
                // 检查下面
                if (i < this.size - 1 && current === this.board[i + 1][j]) {
                    return false;
                }
            }
        }
        
        return true;
    }

    handleWin() {
        audioManager.play('win');
        
        // 创建庆祝粒子效果
        if (particleSystem.isEnabled()) {
            particleSystem.fireworks(window.innerWidth / 2, window.innerHeight / 2);
        }
        
        // 更新统计
        this.updateStatistics(true);
        
        // 检查成就
        this.checkAchievements();
        
        // 显示胜利界面
        this.showGameOverlay('恭喜！', `你达到了 ${this.targetTile}！`, true);
    }

    handleGameOver() {
        audioManager.play('gameOver');
        
        // 更新统计
        this.updateStatistics(false);
        
        // 检查成就
        this.checkAchievements();
        
        // 显示游戏结束界面
        this.showGameOverlay('游戏结束', '没有可用的移动了！', false);
    }

    updateStatistics(won) {
        const stats = storage.get('statistics', {});
        
        stats.gamesPlayed = (stats.gamesPlayed || 0) + 1;
        if (won) {
            stats.gamesWon = (stats.gamesWon || 0) + 1;
        }
        
        stats.totalScore = (stats.totalScore || 0) + this.score;
        stats.totalMoves = (stats.totalMoves || 0) + this.moves;
        stats.totalTime = (stats.totalTime || 0) + this.gameTime;
        
        if (this.score > stats.bestScore) {
            stats.bestScore = this.score;
            this.bestScore = this.score;
        }
        
        // 更新最高瓦片
        const highestTile = this.getHighestTile();
        if (highestTile > (stats.highestTile || 0)) {
            stats.highestTile = highestTile;
        }
        
        // 更新连胜记录
        if (won) {
            stats.streakCurrent = (stats.streakCurrent || 0) + 1;
            if (stats.streakCurrent > (stats.streakBest || 0)) {
                stats.streakBest = stats.streakCurrent;
            }
        } else {
            stats.streakCurrent = 0;
        }
        
        storage.updateStatistics(stats);
    }

    getHighestTile() {
        let highest = 0;
        for (let i = 0; i < this.size; i++) {
            for (let j = 0; j < this.size; j++) {
                if (this.board[i][j] > highest) {
                    highest = this.board[i][j];
                }
            }
        }
        return highest;
    }

    checkAchievements() {
        // 这里会调用成就系统检查各种成就
        if (window.achievementSystem) {
            window.achievementSystem.checkAchievements(this);
        }
    }

    showGameOverlay(title, message, isWin) {
        const overlay = document.getElementById('gameOverlay');
        const titleElement = document.getElementById('overlayTitle');
        const messageElement = document.getElementById('overlayMessage');
        
        if (overlay && titleElement && messageElement) {
            titleElement.textContent = title;
            messageElement.textContent = message;
            overlay.classList.remove('hidden');
            overlay.classList.add('animate-overlay-in');
        }
    }

    updateScore() {
        const scoreElement = document.getElementById('score');
        const bestScoreElement = document.getElementById('bestScore');
        const movesElement = document.getElementById('moves');
        
        if (scoreElement) {
            scoreElement.textContent = Utils.formatNumber(this.score);
            scoreElement.classList.add('animate-score');
            setTimeout(() => scoreElement.classList.remove('animate-score'), 300);
        }
        
        if (bestScoreElement) {
            bestScoreElement.textContent = Utils.formatNumber(this.bestScore);
        }
        
        if (movesElement) {
            movesElement.textContent = this.moves;
        }
    }

    saveGameState() {
        const gameState = {
            board: this.board,
            score: this.score,
            moves: this.moves,
            gameTime: this.gameTime,
            size: this.size,
            gameMode: this.gameMode,
            targetTile: this.targetTile,
            isWon: this.isWon,
            startTime: this.startTime
        };
        
        storage.saveGameState(gameState);
    }

    loadGameState() {
        const gameState = storage.loadGameState();
        if (gameState) {
            this.board = gameState.board;
            this.score = gameState.score;
            this.moves = gameState.moves;
            this.gameTime = gameState.gameTime;
            this.size = gameState.size;
            this.gameMode = gameState.gameMode;
            this.targetTile = gameState.targetTile;
            this.isWon = gameState.isWon;
            this.startTime = gameState.startTime;
            
            this.updateScore();
            return true;
        }
        return false;
    }

    undo() {
        if (!this.canUndo || !this.previousState) return false;
        
        this.board = Utils.deepClone(this.previousState.board);
        this.score = this.previousState.score;
        this.moves = this.previousState.moves;
        this.gameTime = this.previousState.gameTime;
        
        this.canUndo = false;
        this.isGameOver = false;
        
        this.updateScore();
        this.saveGameState();
        
        return true;
    }

    restart() {
        this.score = 0;
        this.moves = 0;
        this.gameTime = 0;
        this.isGameOver = false;
        this.isWon = false;
        this.isPaused = false;
        this.canUndo = false;
        this.previousState = null;
        this.moveHistory = [];
        
        this.initBoard();
        this.addRandomTile();
        this.addRandomTile();
        
        this.startTime = Date.now();
        this.startTimer();
        
        this.updateScore();
        this.saveGameState();
        
        // 隐藏游戏结束界面
        const overlay = document.getElementById('gameOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    pause() {
        this.isPaused = !this.isPaused;
        return this.isPaused;
    }

    getHint() {
        // 简单的提示算法：找到最佳移动方向
        const directions = ['left', 'right', 'up', 'down'];
        let bestDirection = null;
        let bestScore = -1;
        
        for (const direction of directions) {
            const testGame = new Game2048();
            testGame.board = Utils.deepClone(this.board);
            testGame.score = this.score;
            
            if (testGame.move(direction)) {
                const score = testGame.evaluateBoard();
                if (score > bestScore) {
                    bestScore = score;
                    bestDirection = direction;
                }
            }
        }
        
        return bestDirection;
    }

    evaluateBoard() {
        let score = 0;
        
        // 空格数量
        score += this.getEmptyCells().length * 10;
        
        // 最大值在角落
        const corners = [
            this.board[0][0],
            this.board[0][this.size - 1],
            this.board[this.size - 1][0],
            this.board[this.size - 1][this.size - 1]
        ];
        const maxTile = this.getHighestTile();
        if (corners.includes(maxTile)) {
            score += 100;
        }
        
        // 单调性
        score += this.calculateMonotonicity();
        
        return score;
    }

    calculateMonotonicity() {
        let score = 0;
        
        // 检查行的单调性
        for (let i = 0; i < this.size; i++) {
            let increasing = 0;
            let decreasing = 0;
            
            for (let j = 0; j < this.size - 1; j++) {
                if (this.board[i][j] > this.board[i][j + 1]) {
                    decreasing++;
                } else if (this.board[i][j] < this.board[i][j + 1]) {
                    increasing++;
                }
            }
            
            score += Math.max(increasing, decreasing);
        }
        
        // 检查列的单调性
        for (let j = 0; j < this.size; j++) {
            let increasing = 0;
            let decreasing = 0;
            
            for (let i = 0; i < this.size - 1; i++) {
                if (this.board[i][j] > this.board[i + 1][j]) {
                    decreasing++;
                } else if (this.board[i][j] < this.board[i + 1][j]) {
                    increasing++;
                }
            }
            
            score += Math.max(increasing, decreasing);
        }
        
        return score;
    }
}

// 导出游戏类
window.Game2048 = Game2048;
