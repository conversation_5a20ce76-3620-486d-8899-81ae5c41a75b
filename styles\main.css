/* 主样式文件 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #776e65;
    --secondary-color: #f9f6f2;
    --background-color: #faf8ef;
    --text-color: #776e65;
    --border-color: #bbada0;
    --tile-background: #cdc1b4;
    --tile-2: #eee4da;
    --tile-4: #ede0c8;
    --tile-8: #f2b179;
    --tile-16: #f59563;
    --tile-32: #f67c5f;
    --tile-64: #f65e3b;
    --tile-128: #edcf72;
    --tile-256: #edcc61;
    --tile-512: #edc850;
    --tile-1024: #edc53f;
    --tile-2048: #edc22e;
    --tile-super: #3c3a32;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --border-radius: 6px;
    --transition: all 0.15s ease-in-out;
}

body {
    font-family: 'Roboto', sans-serif;
    background: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
}

.app {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
}

/* 头部样式 */
.header {
    margin-bottom: 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.header-controls {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    outline: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 40px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #8f7a66;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: #f0ede4;
    transform: translateY(-1px);
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    font-size: 18px;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
    min-height: 30px;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 选择框样式 */
.select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--secondary-color);
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
    outline: none;
    transition: var(--transition);
}

.select:hover {
    border-color: var(--primary-color);
}

/* 游戏信息面板 */
.game-info {
    margin-bottom: 20px;
}

.score-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.score-box {
    background: var(--secondary-color);
    padding: 15px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.score-label {
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: 5px;
    opacity: 0.8;
}

.score-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

/* 游戏控制面板 */
.game-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.control-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* 游戏容器 */
.game-container {
    position: relative;
    margin: 0 auto;
    background: var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px;
    box-shadow: var(--shadow);
}

.game-board {
    display: grid;
    gap: 10px;
    position: relative;
    background: var(--border-color);
    border-radius: var(--border-radius);
}

.game-board.size-3 {
    grid-template-columns: repeat(3, 1fr);
    width: 300px;
    height: 300px;
}

.game-board.size-4 {
    grid-template-columns: repeat(4, 1fr);
    width: 400px;
    height: 400px;
}

.game-board.size-5 {
    grid-template-columns: repeat(5, 1fr);
    width: 500px;
    height: 500px;
}

.game-board.size-6 {
    grid-template-columns: repeat(6, 1fr);
    width: 600px;
    height: 600px;
}

/* 瓦片样式 */
.tile {
    background: var(--tile-background);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 700;
    color: var(--text-color);
    position: relative;
    transition: var(--transition);
    cursor: default;
}

.tile.tile-2 { background: var(--tile-2); color: var(--text-color); }
.tile.tile-4 { background: var(--tile-4); color: var(--text-color); }
.tile.tile-8 { background: var(--tile-8); color: white; }
.tile.tile-16 { background: var(--tile-16); color: white; }
.tile.tile-32 { background: var(--tile-32); color: white; }
.tile.tile-64 { background: var(--tile-64); color: white; }
.tile.tile-128 { background: var(--tile-128); color: white; font-size: 28px; }
.tile.tile-256 { background: var(--tile-256); color: white; font-size: 28px; }
.tile.tile-512 { background: var(--tile-512); color: white; font-size: 28px; }
.tile.tile-1024 { background: var(--tile-1024); color: white; font-size: 24px; }
.tile.tile-2048 { background: var(--tile-2048); color: white; font-size: 24px; }
.tile.tile-super { background: var(--tile-super); color: white; font-size: 20px; }

.tile.new-tile {
    animation: tileAppear 0.2s ease-in-out;
}

.tile.merged-tile {
    animation: tileMerge 0.2s ease-in-out;
}

/* 游戏覆盖层 */
.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    z-index: 100;
}

.overlay-content {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.overlay-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    justify-content: center;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app {
        padding: 10px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .game-board.size-4 {
        width: 320px;
        height: 320px;
    }
    
    .game-board.size-5 {
        width: 320px;
        height: 320px;
    }
    
    .game-board.size-6 {
        width: 320px;
        height: 320px;
    }
    
    .tile {
        font-size: 24px;
    }
    
    .tile.tile-128,
    .tile.tile-256,
    .tile.tile-512 {
        font-size: 20px;
    }
    
    .tile.tile-1024,
    .tile.tile-2048 {
        font-size: 18px;
    }
    
    .tile.tile-super {
        font-size: 16px;
    }
    
    .control-group {
        justify-content: center;
    }
    
    .score-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .game-board.size-4 {
        width: 280px;
        height: 280px;
    }
    
    .tile {
        font-size: 20px;
    }
    
    .tile.tile-128,
    .tile.tile-256,
    .tile.tile-512,
    .tile.tile-1024,
    .tile.tile-2048 {
        font-size: 16px;
    }
    
    .tile.tile-super {
        font-size: 14px;
    }
}
