/* 动画样式文件 */

/* 瓦片动画 */
@keyframes tileAppear {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes tileMerge {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes tileMove {
    0% {
        transform: translateX(var(--move-x, 0)) translateY(var(--move-y, 0));
    }
    100% {
        transform: translateX(0) translateY(0);
    }
}

/* 分数动画 */
@keyframes scoreIncrease {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        color: #f67c5f;
    }
    100% {
        transform: scale(1);
    }
}

@keyframes floatingScore {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-50px) scale(1.2);
    }
}

/* 游戏板动画 */
@keyframes boardShake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-2px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(2px);
    }
}

@keyframes boardPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

/* 按钮动画 */
@keyframes buttonPress {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes buttonGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(119, 110, 101, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(119, 110, 101, 0.6);
    }
}

/* 覆盖层动画 */
@keyframes overlayFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes overlaySlideIn {
    0% {
        opacity: 0;
        transform: translateY(-50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 侧边栏动画 */
@keyframes sidebarSlideIn {
    0% {
        right: -400px;
    }
    100% {
        right: 0;
    }
}

/* 主题选择器动画 */
@keyframes themePopIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 粒子动画 */
@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: translateY(0) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) rotate(360deg);
    }
}

@keyframes particleExplode {
    0% {
        opacity: 1;
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0);
    }
}

/* 成就解锁动画 */
@keyframes achievementUnlock {
    0% {
        transform: scale(1);
        background: var(--background-color);
    }
    25% {
        transform: scale(1.05);
    }
    50% {
        transform: scale(1.1);
        background: #fff3cd;
    }
    75% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
        background: #d4edda;
    }
}

/* 提示动画 */
@keyframes hintPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(119, 110, 101, 0.7);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(119, 110, 101, 0);
    }
}

/* 加载动画 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* 文字打字机效果 */
@keyframes typewriter {
    0% {
        width: 0;
    }
    100% {
        width: 100%;
    }
}

/* 彩虹文字效果 */
@keyframes rainbow {
    0% { color: #ff0000; }
    16.66% { color: #ff8000; }
    33.33% { color: #ffff00; }
    50% { color: #00ff00; }
    66.66% { color: #0080ff; }
    83.33% { color: #8000ff; }
    100% { color: #ff0000; }
}

/* 霓虹发光效果 */
@keyframes neonGlow {
    0%, 100% {
        text-shadow: 
            0 0 5px currentColor,
            0 0 10px currentColor,
            0 0 15px currentColor;
    }
    50% {
        text-shadow: 
            0 0 10px currentColor,
            0 0 20px currentColor,
            0 0 30px currentColor;
    }
}

/* 动画类 */
.animate-appear {
    animation: tileAppear 0.2s ease-in-out;
}

.animate-merge {
    animation: tileMerge 0.2s ease-in-out;
}

.animate-move {
    animation: tileMove 0.15s ease-in-out;
}

.animate-score {
    animation: scoreIncrease 0.3s ease-in-out;
}

.animate-shake {
    animation: boardShake 0.5s ease-in-out;
}

.animate-pulse {
    animation: boardPulse 0.3s ease-in-out;
}

.animate-button-press {
    animation: buttonPress 0.1s ease-in-out;
}

.animate-glow {
    animation: buttonGlow 2s ease-in-out infinite;
}

.animate-overlay-in {
    animation: overlayFadeIn 0.3s ease-in-out;
}

.animate-slide-in {
    animation: overlaySlideIn 0.3s ease-in-out;
}

.animate-theme-pop {
    animation: themePopIn 0.3s ease-in-out;
}

.animate-achievement {
    animation: achievementUnlock 0.8s ease-in-out;
}

.animate-hint {
    animation: hintPulse 2s ease-in-out infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-pulse-slow {
    animation: pulse 2s ease-in-out infinite;
}

.animate-typewriter {
    animation: typewriter 2s steps(40) 1s both;
}

.animate-rainbow {
    animation: rainbow 3s linear infinite;
}

.animate-neon {
    animation: neonGlow 2s ease-in-out infinite;
}

/* 过渡效果 */
.transition-all {
    transition: all 0.3s ease-in-out;
}

.transition-transform {
    transition: transform 0.3s ease-in-out;
}

.transition-opacity {
    transition: opacity 0.3s ease-in-out;
}

.transition-colors {
    transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
}

/* 悬停效果 */
.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow:hover {
    box-shadow: 0 0 15px rgba(119, 110, 101, 0.3);
}

/* 禁用动画类（用于性能优化） */
.no-animations * {
    animation-duration: 0s !important;
    animation-delay: 0s !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
}

/* 减少动画（用于可访问性） */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 响应式动画调整 */
@media (max-width: 768px) {
    .animate-appear,
    .animate-merge,
    .animate-move {
        animation-duration: 0.15s;
    }
    
    .animate-shake {
        animation-duration: 0.3s;
    }
    
    .animate-overlay-in,
    .animate-slide-in,
    .animate-theme-pop {
        animation-duration: 0.2s;
    }
}
